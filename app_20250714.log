2025-07-14 09:13:44,858 [INFO] 应用程序启动
2025-07-14 09:13:45,011 [INFO] 成功加载base64编码的应用程序图标
2025-07-14 09:13:45,012 [INFO] 应用程序图标已设置
2025-07-14 09:13:45,014 [INFO] 成功加载base64编码的应用程序图标
2025-07-14 09:14:42,893 [INFO] 成功加载base64编码的应用程序图标
2025-07-14 09:14:42,964 [INFO] 开始加载缓存文件: company_cache.dat
2025-07-14 09:14:43,028 [INFO] 缓存文件包含 0 个条目
2025-07-14 09:14:43,082 [INFO] 缓存加载统计: 总计 0 个条目 (新格式: 0, 旧格式: 0, 错误: 0)
2025-07-14 09:14:43,096 [INFO] 缓存加载完成，共 0 个查询条目
2025-07-14 09:14:43,157 [INFO] 开始加载失信人缓存文件: dishonest_cache.dat
2025-07-14 09:14:43,167 [INFO] 失信人缓存文件包含 2 个条目
2025-07-14 09:14:43,226 [INFO] 加载失信人缓存: '联储证券股份有限公司'
2025-07-14 09:14:43,229 [INFO] 加载失信人缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:14:43,284 [INFO] 失信人缓存加载完成，共 2 个查询条目
2025-07-14 09:15:00,986 [INFO] Batch query initiated with keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:15:00,986 [INFO] Starting batch query for 2 keywords
2025-07-14 09:15:00,987 [INFO] Signal connections established
2025-07-14 09:15:00,987 [INFO] Starting worker thread...
2025-07-14 09:15:00,988 [INFO] DishonestQueryWorker.run() started with 2 keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:15:00,988 [INFO] Found cached data for keyword: 联储证券股份有限公司
2025-07-14 09:15:00,988 [INFO] Found cached data for keyword: 北京远翰国际教育咨询有限责任公司
2025-07-14 09:15:00,989 [INFO] Prepared 2 queries for processing
2025-07-14 09:15:00,989 [INFO] Starting query processing...
2025-07-14 09:15:00,990 [INFO] _process_next_query called: is_running=True, current_index=0, pending_count=2
2025-07-14 09:15:00,990 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:15:00,990 [INFO] Processing query 1/2: 联储证券股份有限公司
2025-07-14 09:15:00,990 [INFO] Found cache for 联储证券股份有限公司, prompting user
2025-07-14 09:15:01,158 [INFO] Batch query finished handler called
2025-07-14 09:15:01,159 [INFO] Batch query statistics: successful=0, failed=0, total=0
2025-07-14 09:15:05,870 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-14 09:15:07,785 [INFO] ✅ 失信人查询成功: '联储证券股份有限公司' (来源: 缓存)
2025-07-14 09:15:07,785 [INFO] Moving to next query: index now 1/2
2025-07-14 09:15:08,794 [INFO] _process_next_query called: is_running=True, current_index=1, pending_count=2
2025-07-14 09:15:08,795 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:15:08,796 [INFO] Processing query 2/2: 北京远翰国际教育咨询有限责任公司
2025-07-14 09:15:08,798 [INFO] Found cache for 北京远翰国际教育咨询有限责任公司, prompting user
2025-07-14 09:15:11,658 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-14 09:15:11,687 [INFO] 显示结果: '北京远翰国际教育咨询有限责任公司', 缓存状态: True
2025-07-14 09:15:11,694 [INFO] ✅ 失信人查询成功: '北京远翰国际教育咨询有限责任公司' (来源: 缓存)
2025-07-14 09:15:11,695 [INFO] Moving to next query: index now 2/2
2025-07-14 09:15:11,695 [INFO] All queries completed, will finish on next _process_next_query call
2025-07-14 09:15:11,695 [INFO] _process_next_query called: is_running=True, current_index=2, pending_count=2
2025-07-14 09:15:11,696 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:15:11,696 [INFO] All queries processed, finishing
2025-07-14 09:15:11,696 [INFO] Batch query finished handler called
2025-07-14 09:15:11,696 [INFO] Batch query statistics: successful=2, failed=0, total=2
2025-07-14 09:15:14,049 [INFO] 批量查询完成 - 成功: 2, 失败: 0
2025-07-14 09:15:16,776 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-14 09:15:18,559 [INFO] 显示结果: '北京远翰国际教育咨询有限责任公司', 缓存状态: True
2025-07-14 09:15:40,794 [INFO] 开始失信人查询: '联储证券股份有限公司'
2025-07-14 09:15:40,794 [INFO] ✅ 在失信人缓存中找到: '联储证券股份有限公司'
2025-07-14 09:15:42,806 [INFO] 发起失信人API查询: '联储证券股份有限公司'
2025-07-14 09:15:43,089 [INFO] 开始缓存失信人查询结果，查询字符串: '联储证券股份有限公司'
2025-07-14 09:15:43,090 [INFO] 使用失信人缓存键: '联储证券股份有限公司'
2025-07-14 09:15:43,097 [INFO] 已保存到失信人持久化缓存: '联储证券股份有限公司'
2025-07-14 09:15:43,137 [INFO] 已添加到失信人内存缓存: '联储证券股份有限公司'
2025-07-14 09:15:46,193 [INFO] ✅ 失信人查询成功（无记录）: '联储证券股份有限公司'
2025-07-14 09:17:52,211 [INFO] Batch query initiated with keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:17:52,212 [INFO] Starting batch query for 2 keywords
2025-07-14 09:17:52,212 [INFO] Signal connections established
2025-07-14 09:17:52,213 [INFO] Starting worker thread...
2025-07-14 09:17:52,213 [INFO] DishonestQueryWorker.run() started with 2 keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:17:52,214 [INFO] Found cached data for keyword: 联储证券股份有限公司
2025-07-14 09:17:52,214 [INFO] Found cached data for keyword: 北京远翰国际教育咨询有限责任公司
2025-07-14 09:17:52,214 [INFO] Prepared 2 queries for processing
2025-07-14 09:17:52,215 [INFO] Starting query processing...
2025-07-14 09:17:52,215 [INFO] _process_next_query called: is_running=True, current_index=0, pending_count=2
2025-07-14 09:17:52,215 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:17:52,216 [INFO] Processing query 1/2: 联储证券股份有限公司
2025-07-14 09:17:52,216 [INFO] Found cache for 联储证券股份有限公司, prompting user
2025-07-14 09:17:52,244 [INFO] Batch query finished handler called
2025-07-14 09:17:52,244 [INFO] Batch query statistics: successful=0, failed=0, total=0
2025-07-14 09:17:55,445 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-14 09:18:00,220 [INFO] ✅ 失信人查询成功: '联储证券股份有限公司' (来源: 缓存)
2025-07-14 09:18:00,221 [INFO] Moving to next query: index now 1/2
2025-07-14 09:18:01,230 [INFO] _process_next_query called: is_running=True, current_index=1, pending_count=2
2025-07-14 09:18:01,230 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:18:01,231 [INFO] Processing query 2/2: 北京远翰国际教育咨询有限责任公司
2025-07-14 09:18:01,232 [INFO] Found cache for 北京远翰国际教育咨询有限责任公司, prompting user
2025-07-14 09:18:02,497 [INFO] 开始缓存失信人查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:18:02,497 [INFO] 使用失信人缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:18:02,502 [INFO] 已保存到失信人持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:18:02,522 [INFO] 已添加到失信人内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:18:02,525 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-14 09:18:02,535 [INFO] 显示结果: '北京远翰国际教育咨询有限责任公司', 缓存状态: False
2025-07-14 09:18:02,543 [INFO] ✅ 失信人查询成功: '北京远翰国际教育咨询有限责任公司' (来源: API)
2025-07-14 09:18:02,543 [INFO] Moving to next query: index now 2/2
2025-07-14 09:18:02,543 [INFO] All queries completed, will finish on next _process_next_query call
2025-07-14 09:18:02,543 [INFO] _process_next_query called: is_running=True, current_index=2, pending_count=2
2025-07-14 09:18:02,545 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:18:02,545 [INFO] All queries processed, finishing
2025-07-14 09:18:02,545 [INFO] Batch query finished handler called
2025-07-14 09:18:02,546 [INFO] Batch query statistics: successful=2, failed=0, total=2
2025-07-14 09:18:04,411 [INFO] 批量查询完成 - 成功: 2, 失败: 0
2025-07-14 09:18:11,642 [INFO] Batch query initiated with keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:18:11,642 [INFO] Starting batch query for 2 keywords
2025-07-14 09:18:11,642 [INFO] Signal connections established
2025-07-14 09:18:11,643 [INFO] Starting worker thread...
2025-07-14 09:18:11,644 [INFO] DishonestQueryWorker.run() started with 2 keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:18:11,644 [INFO] No cache for keyword, will query API: 联储证券股份有限公司
2025-07-14 09:18:11,644 [INFO] No cache for keyword, will query API: 北京远翰国际教育咨询有限责任公司
2025-07-14 09:18:11,645 [INFO] Prepared 2 queries for processing
2025-07-14 09:18:11,645 [INFO] Starting query processing...
2025-07-14 09:18:11,645 [INFO] _process_next_query called: is_running=True, current_index=0, pending_count=2
2025-07-14 09:18:11,645 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:18:11,646 [INFO] Processing query 1/2: 联储证券股份有限公司
2025-07-14 09:18:11,646 [INFO] No cache for 联储证券股份有限公司, executing API query
2025-07-14 09:18:11,809 [INFO] Moving to next query: index now 1/2
2025-07-14 09:18:11,814 [INFO] 开始缓存失信人查询结果，查询字符串: '联储证券股份有限公司'
2025-07-14 09:18:11,815 [INFO] 使用失信人缓存键: '联储证券股份有限公司'
2025-07-14 09:18:11,826 [INFO] 已保存到失信人持久化缓存: '联储证券股份有限公司'
2025-07-14 09:18:11,843 [INFO] 已添加到失信人内存缓存: '联储证券股份有限公司'
2025-07-14 09:18:11,845 [INFO] ✅ 失信人查询成功: '联储证券股份有限公司' (来源: API)
2025-07-14 09:18:12,833 [INFO] _process_next_query called: is_running=True, current_index=1, pending_count=2
2025-07-14 09:18:12,834 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:18:12,835 [INFO] Processing query 2/2: 北京远翰国际教育咨询有限责任公司
2025-07-14 09:18:12,836 [INFO] No cache for 北京远翰国际教育咨询有限责任公司, executing API query
2025-07-14 09:18:12,993 [INFO] 开始缓存失信人查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:18:12,995 [INFO] 使用失信人缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:18:13,013 [INFO] 已保存到失信人持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:18:13,037 [INFO] 已添加到失信人内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:18:13,039 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: False
2025-07-14 09:18:13,054 [INFO] 显示结果: '北京远翰国际教育咨询有限责任公司', 缓存状态: False
2025-07-14 09:18:13,065 [INFO] ✅ 失信人查询成功: '北京远翰国际教育咨询有限责任公司' (来源: API)
2025-07-14 09:18:13,066 [INFO] Moving to next query: index now 2/2
2025-07-14 09:18:13,066 [INFO] All queries completed, will finish on next _process_next_query call
2025-07-14 09:18:13,066 [INFO] _process_next_query called: is_running=True, current_index=2, pending_count=2
2025-07-14 09:18:13,067 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:18:13,067 [INFO] All queries processed, finishing
2025-07-14 09:18:13,067 [INFO] Batch query finished handler called
2025-07-14 09:18:13,067 [INFO] Batch query statistics: successful=2, failed=0, total=2
2025-07-14 09:18:13,093 [INFO] Batch query finished handler called
2025-07-14 09:18:13,094 [INFO] Batch query statistics: successful=2, failed=0, total=2
2025-07-14 09:18:14,541 [INFO] 批量查询完成 - 成功: 2, 失败: 0
2025-07-14 09:18:17,884 [INFO] 批量查询完成 - 成功: 2, 失败: 0
2025-07-14 09:18:40,459 [INFO] Batch query initiated with keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司', '光大保德信基金管理有限公司']
2025-07-14 09:18:40,460 [INFO] Starting batch query for 3 keywords
2025-07-14 09:18:40,462 [INFO] Signal connections established
2025-07-14 09:18:40,463 [INFO] Starting worker thread...
2025-07-14 09:18:40,464 [INFO] DishonestQueryWorker.run() started with 3 keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司', '光大保德信基金管理有限公司']
2025-07-14 09:18:40,465 [INFO] No cache for keyword, will query API: 联储证券股份有限公司
2025-07-14 09:18:40,466 [INFO] No cache for keyword, will query API: 北京远翰国际教育咨询有限责任公司
2025-07-14 09:18:40,467 [INFO] No cache for keyword, will query API: 光大保德信基金管理有限公司
2025-07-14 09:18:40,468 [INFO] Prepared 3 queries for processing
2025-07-14 09:18:40,468 [INFO] Starting query processing...
2025-07-14 09:18:40,469 [INFO] _process_next_query called: is_running=True, current_index=0, pending_count=3
2025-07-14 09:18:40,470 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司', '光大保德信基金管理有限公司']
2025-07-14 09:18:40,471 [INFO] Processing query 1/3: 联储证券股份有限公司
2025-07-14 09:18:40,472 [INFO] No cache for 联储证券股份有限公司, executing API query
2025-07-14 09:18:40,654 [INFO] Moving to next query: index now 1/3
2025-07-14 09:18:40,662 [INFO] 开始缓存失信人查询结果，查询字符串: '联储证券股份有限公司'
2025-07-14 09:18:40,663 [INFO] 使用失信人缓存键: '联储证券股份有限公司'
2025-07-14 09:18:40,685 [INFO] 已保存到失信人持久化缓存: '联储证券股份有限公司'
2025-07-14 09:18:40,742 [INFO] 已添加到失信人内存缓存: '联储证券股份有限公司'
2025-07-14 09:18:40,746 [INFO] ✅ 失信人查询成功: '联储证券股份有限公司' (来源: API)
2025-07-14 09:18:41,662 [INFO] _process_next_query called: is_running=True, current_index=1, pending_count=3
2025-07-14 09:18:41,664 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司', '光大保德信基金管理有限公司']
2025-07-14 09:18:41,665 [INFO] Processing query 2/3: 北京远翰国际教育咨询有限责任公司
2025-07-14 09:18:41,666 [INFO] No cache for 北京远翰国际教育咨询有限责任公司, executing API query
2025-07-14 09:18:41,823 [INFO] 开始缓存失信人查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:18:41,824 [INFO] 使用失信人缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:18:41,851 [INFO] 已保存到失信人持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:18:41,901 [INFO] 已添加到失信人内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:18:41,904 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: False
2025-07-14 09:18:41,918 [INFO] 显示结果: '北京远翰国际教育咨询有限责任公司', 缓存状态: False
2025-07-14 09:18:41,931 [INFO] ✅ 失信人查询成功: '北京远翰国际教育咨询有限责任公司' (来源: API)
2025-07-14 09:18:41,931 [INFO] Moving to next query: index now 2/3
2025-07-14 09:18:42,932 [INFO] _process_next_query called: is_running=True, current_index=2, pending_count=3
2025-07-14 09:18:42,933 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司', '光大保德信基金管理有限公司']
2025-07-14 09:18:42,935 [INFO] Processing query 3/3: 光大保德信基金管理有限公司
2025-07-14 09:18:42,935 [INFO] No cache for 光大保德信基金管理有限公司, executing API query
2025-07-14 09:18:43,095 [INFO] 开始缓存失信人查询结果，查询字符串: '光大保德信基金管理有限公司'
2025-07-14 09:18:43,096 [INFO] 使用失信人缓存键: '光大保德信基金管理有限公司'
2025-07-14 09:18:43,108 [INFO] 已保存到失信人持久化缓存: '光大保德信基金管理有限公司'
2025-07-14 09:18:43,138 [INFO] 已添加到失信人内存缓存: '光大保德信基金管理有限公司'
2025-07-14 09:18:43,214 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: False
2025-07-14 09:18:43,215 [INFO] 显示结果: '光大保德信基金管理有限公司', 缓存状态: False
2025-07-14 09:18:43,216 [INFO] ✅ 失信人查询成功: '光大保德信基金管理有限公司' (来源: API)
2025-07-14 09:18:43,216 [INFO] Moving to next query: index now 3/3
2025-07-14 09:18:43,216 [INFO] All queries completed, will finish on next _process_next_query call
2025-07-14 09:18:43,217 [INFO] _process_next_query called: is_running=True, current_index=3, pending_count=3
2025-07-14 09:18:43,217 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司', '光大保德信基金管理有限公司']
2025-07-14 09:18:43,218 [INFO] All queries processed, finishing
2025-07-14 09:18:43,218 [INFO] Batch query finished handler called
2025-07-14 09:18:43,218 [INFO] Batch query statistics: successful=3, failed=0, total=3
2025-07-14 09:18:43,245 [INFO] Batch query finished handler called
2025-07-14 09:18:43,246 [INFO] Batch query statistics: successful=3, failed=0, total=3
2025-07-14 09:18:44,623 [INFO] 批量查询完成 - 成功: 3, 失败: 0
2025-07-14 09:18:46,525 [INFO] 批量查询完成 - 成功: 3, 失败: 0
2025-07-14 09:18:53,395 [INFO] 显示结果: '北京远翰国际教育咨询有限责任公司', 缓存状态: False
2025-07-14 09:18:54,952 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: False
2025-07-14 09:18:59,987 [INFO] Batch query initiated with keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司', '光大保德信基金管理有限公司']
2025-07-14 09:18:59,987 [INFO] Starting batch query for 3 keywords
2025-07-14 09:18:59,988 [INFO] Signal connections established
2025-07-14 09:18:59,988 [INFO] Starting worker thread...
2025-07-14 09:18:59,989 [INFO] DishonestQueryWorker.run() started with 3 keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司', '光大保德信基金管理有限公司']
2025-07-14 09:18:59,989 [INFO] Found cached data for keyword: 联储证券股份有限公司
2025-07-14 09:18:59,989 [INFO] Found cached data for keyword: 北京远翰国际教育咨询有限责任公司
2025-07-14 09:18:59,990 [INFO] Found cached data for keyword: 光大保德信基金管理有限公司
2025-07-14 09:18:59,990 [INFO] Prepared 3 queries for processing
2025-07-14 09:18:59,990 [INFO] Starting query processing...
2025-07-14 09:18:59,990 [INFO] _process_next_query called: is_running=True, current_index=0, pending_count=3
2025-07-14 09:18:59,991 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司', '光大保德信基金管理有限公司']
2025-07-14 09:18:59,992 [INFO] Processing query 1/3: 联储证券股份有限公司
2025-07-14 09:18:59,992 [INFO] Found cache for 联储证券股份有限公司, prompting user
2025-07-14 09:19:00,021 [INFO] Batch query finished handler called
2025-07-14 09:19:00,022 [INFO] Batch query statistics: successful=0, failed=0, total=0
2025-07-14 09:19:03,158 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-14 09:19:04,674 [INFO] ✅ 失信人查询成功: '联储证券股份有限公司' (来源: 缓存)
2025-07-14 09:19:04,675 [INFO] Moving to next query: index now 1/3
2025-07-14 09:19:05,684 [INFO] _process_next_query called: is_running=True, current_index=1, pending_count=3
2025-07-14 09:19:05,686 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司', '光大保德信基金管理有限公司']
2025-07-14 09:19:05,687 [INFO] Processing query 2/3: 北京远翰国际教育咨询有限责任公司
2025-07-14 09:19:05,688 [INFO] Found cache for 北京远翰国际教育咨询有限责任公司, prompting user
2025-07-14 09:19:07,216 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-14 09:19:07,224 [INFO] 显示结果: '北京远翰国际教育咨询有限责任公司', 缓存状态: True
2025-07-14 09:19:07,233 [INFO] ✅ 失信人查询成功: '北京远翰国际教育咨询有限责任公司' (来源: 缓存)
2025-07-14 09:19:07,234 [INFO] Moving to next query: index now 2/3
2025-07-14 09:19:08,235 [INFO] _process_next_query called: is_running=True, current_index=2, pending_count=3
2025-07-14 09:19:08,236 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司', '光大保德信基金管理有限公司']
2025-07-14 09:19:08,237 [INFO] Processing query 3/3: 光大保德信基金管理有限公司
2025-07-14 09:19:08,238 [INFO] Found cache for 光大保德信基金管理有限公司, prompting user
2025-07-14 09:19:08,950 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-14 09:19:08,952 [INFO] 显示结果: '光大保德信基金管理有限公司', 缓存状态: True
2025-07-14 09:19:08,954 [INFO] ✅ 失信人查询成功: '光大保德信基金管理有限公司' (来源: 缓存)
2025-07-14 09:19:08,954 [INFO] Moving to next query: index now 3/3
2025-07-14 09:19:08,955 [INFO] All queries completed, will finish on next _process_next_query call
2025-07-14 09:19:08,956 [INFO] _process_next_query called: is_running=True, current_index=3, pending_count=3
2025-07-14 09:19:08,956 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司', '光大保德信基金管理有限公司']
2025-07-14 09:19:08,957 [INFO] All queries processed, finishing
2025-07-14 09:19:08,957 [INFO] Batch query finished handler called
2025-07-14 09:19:08,958 [INFO] Batch query statistics: successful=3, failed=0, total=3
2025-07-14 09:19:10,672 [INFO] 批量查询完成 - 成功: 3, 失败: 0
2025-07-14 09:22:01,766 [INFO] 开始缓存查询结果，查询字符串: '联储证券股份有限公司'
2025-07-14 09:22:01,766 [INFO] 使用缓存键: '联储证券股份有限公司'
2025-07-14 09:22:01,775 [INFO] 已保存到持久化缓存: '联储证券股份有限公司'
2025-07-14 09:22:01,789 [INFO] 已添加到内存缓存: '联储证券股份有限公司'
2025-07-14 09:22:01,790 [INFO] ✅ 缓存完成: 查询'联储证券股份有限公司' -> 企业(信用代码: 91440300727132290A)
2025-07-14 09:22:01,793 [INFO] ✅ 验证成功: '联储证券股份有限公司' 已存在于持久化缓存中
2025-07-14 09:22:02,976 [INFO] 开始缓存查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:22:02,976 [INFO] 使用缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:22:02,983 [INFO] 已保存到持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:22:02,998 [INFO] 已添加到内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:22:02,998 [INFO] ✅ 缓存完成: 查询'北京远翰国际教育咨询有限责任公司' -> 企业(信用代码: 91110107551400801W)
2025-07-14 09:22:03,001 [INFO] ✅ 验证成功: '北京远翰国际教育咨询有限责任公司' 已存在于持久化缓存中
2025-07-14 09:22:06,363 [INFO] 开始缓存查询结果，查询字符串: '联储证券股份有限公司'
2025-07-14 09:22:06,363 [INFO] 使用缓存键: '联储证券股份有限公司'
2025-07-14 09:22:06,368 [INFO] 已保存到持久化缓存: '联储证券股份有限公司'
2025-07-14 09:22:06,384 [INFO] 已添加到内存缓存: '联储证券股份有限公司'
2025-07-14 09:22:06,385 [INFO] ✅ 缓存完成: 查询'联储证券股份有限公司' -> 企业(信用代码: 91440300727132290A)
2025-07-14 09:22:06,387 [INFO] ✅ 验证成功: '联储证券股份有限公司' 已存在于持久化缓存中
2025-07-14 09:22:07,907 [INFO] 开始缓存查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:22:07,907 [INFO] 使用缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:22:07,910 [INFO] 已保存到持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:22:07,928 [INFO] 已添加到内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:22:07,929 [INFO] ✅ 缓存完成: 查询'北京远翰国际教育咨询有限责任公司' -> 企业(信用代码: 91110107551400801W)
2025-07-14 09:22:07,932 [INFO] ✅ 验证成功: '北京远翰国际教育咨询有限责任公司' 已存在于持久化缓存中
2025-07-14 09:22:17,382 [INFO] 开始批量导出 2 个企业到Excel，使用显示配置
2025-07-14 09:22:17,382 [INFO] 生成了 88 个导出列
2025-07-14 09:22:17,385 [INFO] 成功处理企业: 联储证券股份有限公司 -> 联储证券股份有限公司
2025-07-14 09:22:17,386 [INFO] 成功处理企业: 北京远翰国际教育咨询有限责任公司 -> 北京远翰国际教育咨询有限责任公司
2025-07-14 09:22:17,426 [INFO] 批量导出完成: C:/Users/<USER>/Desktop/企业信息批量导出_20250714_092213.xlsx, 共 2 行数据
2025-07-14 09:22:36,492 [INFO] 应用程序退出，退出代码：0
2025-07-14 09:22:39,806 [INFO] 应用程序启动
2025-07-14 09:22:39,984 [INFO] 成功加载base64编码的应用程序图标
2025-07-14 09:22:39,984 [INFO] 应用程序图标已设置
2025-07-14 09:22:39,986 [INFO] 成功加载base64编码的应用程序图标
2025-07-14 09:22:44,688 [INFO] 成功加载base64编码的应用程序图标
2025-07-14 09:22:44,691 [INFO] 开始加载缓存文件: company_cache.dat
2025-07-14 09:22:44,702 [INFO] 缓存文件包含 2 个条目
2025-07-14 09:22:44,704 [INFO] 处理缓存条目: '联储证券股份有限公司'
2025-07-14 09:22:44,706 [INFO] 加载新格式缓存: '联储证券股份有限公司'
2025-07-14 09:22:44,706 [INFO] 处理缓存条目: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:22:44,707 [INFO] 加载新格式缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:22:44,707 [INFO] 缓存加载统计: 总计 2 个条目 (新格式: 2, 旧格式: 0, 错误: 0)
2025-07-14 09:22:44,707 [INFO] 缓存加载完成，共 2 个查询条目
2025-07-14 09:22:44,708 [INFO] 可用的缓存键示例: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:22:44,724 [INFO] 开始加载失信人缓存文件: dishonest_cache.dat
2025-07-14 09:22:44,730 [INFO] 失信人缓存文件包含 3 个条目
2025-07-14 09:22:44,731 [INFO] 加载失信人缓存: '联储证券股份有限公司'
2025-07-14 09:22:44,732 [INFO] 加载失信人缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:22:44,733 [INFO] 加载失信人缓存: '光大保德信基金管理有限公司'
2025-07-14 09:22:44,734 [INFO] 失信人缓存加载完成，共 3 个查询条目
2025-07-14 09:22:50,033 [INFO] 开始缓存查询结果，查询字符串: '联储证券股份有限公司'
2025-07-14 09:22:50,034 [INFO] 使用缓存键: '联储证券股份有限公司'
2025-07-14 09:22:50,039 [INFO] 已保存到持久化缓存: '联储证券股份有限公司'
2025-07-14 09:22:50,065 [INFO] 已添加到内存缓存: '联储证券股份有限公司'
2025-07-14 09:22:50,066 [INFO] ✅ 缓存完成: 查询'联储证券股份有限公司' -> 企业(信用代码: 91440300727132290A)
2025-07-14 09:22:50,069 [INFO] ✅ 验证成功: '联储证券股份有限公司' 已存在于持久化缓存中
2025-07-14 09:22:51,231 [INFO] 开始缓存查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:22:51,232 [INFO] 使用缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:22:51,235 [INFO] 已保存到持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:22:51,256 [INFO] 已添加到内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:22:51,256 [INFO] ✅ 缓存完成: 查询'北京远翰国际教育咨询有限责任公司' -> 企业(信用代码: 91110107551400801W)
2025-07-14 09:22:51,259 [INFO] ✅ 验证成功: '北京远翰国际教育咨询有限责任公司' 已存在于持久化缓存中
2025-07-14 09:23:29,816 [INFO] 开始查询: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:23:29,817 [INFO] 当前缓存中有 2 个条目
2025-07-14 09:23:29,817 [INFO] ✅ 在缓存中找到精确匹配: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:23:31,961 [INFO] 用户选择重新查询
2025-07-14 09:23:31,962 [INFO] 发起API查询: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:23:32,225 [INFO] 开始缓存查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:23:32,225 [INFO] 使用缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:23:32,229 [INFO] 已保存到持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:23:32,248 [INFO] 已添加到内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:23:32,248 [INFO] ✅ 缓存完成: 查询'北京远翰国际教育咨询有限责任公司' -> 企业(信用代码: 91110107551400801W)
2025-07-14 09:23:32,251 [INFO] ✅ 验证成功: '北京远翰国际教育咨询有限责任公司' 已存在于持久化缓存中
2025-07-14 09:23:32,256 [INFO] ✅ 查询成功: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:25:14,513 [INFO] Batch query initiated with keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:25:14,513 [INFO] Starting batch query for 2 keywords
2025-07-14 09:25:14,514 [INFO] Signal connections established
2025-07-14 09:25:14,514 [INFO] Starting worker thread...
2025-07-14 09:25:14,514 [INFO] DishonestQueryWorker.run() started with 2 keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:25:14,515 [INFO] Found cached data for keyword: 联储证券股份有限公司
2025-07-14 09:25:14,515 [INFO] Found cached data for keyword: 北京远翰国际教育咨询有限责任公司
2025-07-14 09:25:14,515 [INFO] Prepared 2 queries for processing
2025-07-14 09:25:14,515 [INFO] Starting query processing...
2025-07-14 09:25:14,516 [INFO] _process_next_query called: is_running=True, current_index=0, pending_count=2
2025-07-14 09:25:14,516 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:25:14,516 [INFO] Processing query 1/2: 联储证券股份有限公司
2025-07-14 09:25:14,516 [INFO] Found cache for 联储证券股份有限公司, prompting user
2025-07-14 09:25:14,546 [INFO] Batch query finished handler called
2025-07-14 09:25:14,546 [INFO] Batch query statistics: successful=0, failed=0, total=0
2025-07-14 09:28:54,600 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-14 09:28:55,687 [INFO] ✅ 失信人查询成功: '联储证券股份有限公司' (来源: 缓存)
2025-07-14 09:28:55,687 [INFO] Moving to next query: index now 1/2
2025-07-14 09:28:56,697 [INFO] _process_next_query called: is_running=True, current_index=1, pending_count=2
2025-07-14 09:28:56,698 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:28:56,698 [INFO] Processing query 2/2: 北京远翰国际教育咨询有限责任公司
2025-07-14 09:28:56,698 [INFO] Found cache for 北京远翰国际教育咨询有限责任公司, prompting user
2025-07-14 09:28:57,246 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-14 09:28:57,286 [INFO] 显示结果: '北京远翰国际教育咨询有限责任公司', 缓存状态: True
2025-07-14 09:28:57,299 [INFO] ✅ 失信人查询成功: '北京远翰国际教育咨询有限责任公司' (来源: 缓存)
2025-07-14 09:28:57,300 [INFO] Moving to next query: index now 2/2
2025-07-14 09:28:57,300 [INFO] All queries completed, will finish on next _process_next_query call
2025-07-14 09:28:57,301 [INFO] _process_next_query called: is_running=True, current_index=2, pending_count=2
2025-07-14 09:28:57,301 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:28:57,302 [INFO] All queries processed, finishing
2025-07-14 09:28:57,302 [INFO] Batch query finished handler called
2025-07-14 09:28:57,302 [INFO] Batch query statistics: successful=2, failed=0, total=2
2025-07-14 09:34:18,203 [INFO] 批量查询完成 - 成功: 2, 失败: 0
2025-07-14 09:37:09,706 [INFO] Batch query initiated with keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:37:09,707 [INFO] Starting batch query for 2 keywords
2025-07-14 09:37:09,708 [INFO] Signal connections established
2025-07-14 09:37:09,709 [INFO] Starting worker thread...
2025-07-14 09:37:09,710 [INFO] DishonestQueryWorker.run() started with 2 keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:37:09,711 [INFO] Found cached data for keyword: 联储证券股份有限公司
2025-07-14 09:37:09,711 [INFO] Found cached data for keyword: 北京远翰国际教育咨询有限责任公司
2025-07-14 09:37:09,712 [INFO] Prepared 2 queries for processing
2025-07-14 09:37:09,712 [INFO] Starting query processing...
2025-07-14 09:37:09,713 [INFO] _process_next_query called: is_running=True, current_index=0, pending_count=2
2025-07-14 09:37:09,713 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:37:09,713 [INFO] Processing query 1/2: 联储证券股份有限公司
2025-07-14 09:37:09,714 [INFO] Found cache for 联储证券股份有限公司, prompting user
2025-07-14 09:37:09,770 [INFO] Batch query finished handler called
2025-07-14 09:37:09,771 [INFO] Batch query statistics: successful=0, failed=0, total=0
2025-07-14 09:37:11,032 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-14 09:37:12,432 [INFO] ✅ 失信人查询成功: '联储证券股份有限公司' (来源: 缓存)
2025-07-14 09:37:12,433 [INFO] Moving to next query: index now 1/2
2025-07-14 09:37:13,446 [INFO] _process_next_query called: is_running=True, current_index=1, pending_count=2
2025-07-14 09:37:13,447 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:37:13,448 [INFO] Processing query 2/2: 北京远翰国际教育咨询有限责任公司
2025-07-14 09:37:13,449 [INFO] Found cache for 北京远翰国际教育咨询有限责任公司, prompting user
2025-07-14 09:37:14,135 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-14 09:37:14,159 [INFO] 显示结果: '北京远翰国际教育咨询有限责任公司', 缓存状态: True
2025-07-14 09:37:14,177 [INFO] ✅ 失信人查询成功: '北京远翰国际教育咨询有限责任公司' (来源: 缓存)
2025-07-14 09:37:14,177 [INFO] Moving to next query: index now 2/2
2025-07-14 09:37:14,178 [INFO] All queries completed, will finish on next _process_next_query call
2025-07-14 09:37:14,178 [INFO] _process_next_query called: is_running=True, current_index=2, pending_count=2
2025-07-14 09:37:14,179 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:37:14,179 [INFO] All queries processed, finishing
2025-07-14 09:37:14,180 [INFO] Batch query finished handler called
2025-07-14 09:37:14,180 [INFO] Batch query statistics: successful=2, failed=0, total=2
2025-07-14 09:37:16,319 [INFO] 批量查询完成 - 成功: 2, 失败: 0
2025-07-14 09:37:34,085 [INFO] 开始失信人查询: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:37:34,085 [INFO] ✅ 在失信人缓存中找到: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:37:36,074 [INFO] 用户选择使用失信人缓存数据
2025-07-14 09:37:37,900 [INFO] 开始失信人查询: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:37:37,900 [INFO] ✅ 在失信人缓存中找到: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:37:38,710 [INFO] 发起失信人API查询: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:37:38,878 [INFO] 开始缓存失信人查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:37:38,878 [INFO] 使用失信人缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:37:38,881 [INFO] 已保存到失信人持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:37:38,894 [INFO] 已添加到失信人内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:37:38,902 [INFO] ✅ 失信人查询成功: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:37:53,480 [INFO] Batch query initiated with keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:37:53,481 [INFO] Starting batch query for 2 keywords
2025-07-14 09:37:53,481 [INFO] Signal connections established
2025-07-14 09:37:53,482 [INFO] Starting worker thread...
2025-07-14 09:37:53,482 [INFO] DishonestQueryWorker.run() started with 2 keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:37:53,482 [INFO] No cache for keyword, will query API: 联储证券股份有限公司
2025-07-14 09:37:53,483 [INFO] No cache for keyword, will query API: 北京远翰国际教育咨询有限责任公司
2025-07-14 09:37:53,483 [INFO] Prepared 2 queries for processing
2025-07-14 09:37:53,483 [INFO] Starting query processing...
2025-07-14 09:37:53,483 [INFO] _process_next_query called: is_running=True, current_index=0, pending_count=2
2025-07-14 09:37:53,483 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:37:53,484 [INFO] Processing query 1/2: 联储证券股份有限公司
2025-07-14 09:37:53,484 [INFO] No cache for 联储证券股份有限公司, executing API query
2025-07-14 09:37:53,650 [INFO] Moving to next query: index now 1/2
2025-07-14 09:37:53,651 [INFO] 开始缓存失信人查询结果，查询字符串: '联储证券股份有限公司'
2025-07-14 09:37:53,652 [INFO] 使用失信人缓存键: '联储证券股份有限公司'
2025-07-14 09:37:53,673 [INFO] 已保存到失信人持久化缓存: '联储证券股份有限公司'
2025-07-14 09:37:53,703 [INFO] 已添加到失信人内存缓存: '联储证券股份有限公司'
2025-07-14 09:37:53,705 [INFO] ✅ 失信人查询成功: '联储证券股份有限公司' (来源: API)
2025-07-14 09:37:54,662 [INFO] _process_next_query called: is_running=True, current_index=1, pending_count=2
2025-07-14 09:37:54,663 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:37:54,663 [INFO] Processing query 2/2: 北京远翰国际教育咨询有限责任公司
2025-07-14 09:37:54,663 [INFO] No cache for 北京远翰国际教育咨询有限责任公司, executing API query
2025-07-14 09:37:54,826 [INFO] 开始缓存失信人查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:37:54,827 [INFO] 使用失信人缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:37:54,833 [INFO] 已保存到失信人持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:37:54,850 [INFO] 已添加到失信人内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 09:37:54,852 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: False
2025-07-14 09:37:54,865 [INFO] 显示结果: '北京远翰国际教育咨询有限责任公司', 缓存状态: False
2025-07-14 09:37:54,877 [INFO] ✅ 失信人查询成功: '北京远翰国际教育咨询有限责任公司' (来源: API)
2025-07-14 09:37:54,877 [INFO] Moving to next query: index now 2/2
2025-07-14 09:37:54,878 [INFO] All queries completed, will finish on next _process_next_query call
2025-07-14 09:37:54,878 [INFO] _process_next_query called: is_running=True, current_index=2, pending_count=2
2025-07-14 09:37:54,878 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 09:37:54,878 [INFO] All queries processed, finishing
2025-07-14 09:37:54,879 [INFO] Batch query finished handler called
2025-07-14 09:37:54,879 [INFO] Batch query statistics: successful=2, failed=0, total=2
2025-07-14 09:37:54,902 [INFO] Batch query finished handler called
2025-07-14 09:37:54,902 [INFO] Batch query statistics: successful=2, failed=0, total=2
2025-07-14 09:40:13,143 [INFO] 批量查询完成 - 成功: 2, 失败: 0
2025-07-14 09:40:14,193 [INFO] 批量查询完成 - 成功: 2, 失败: 0
2025-07-14 09:40:19,317 [INFO] 应用程序退出，退出代码：0
2025-07-14 09:47:05,783 [INFO] 应用程序启动
2025-07-14 09:47:05,919 [INFO] 成功加载base64编码的应用程序图标
2025-07-14 09:47:05,919 [INFO] 应用程序图标已设置
2025-07-14 09:47:05,922 [INFO] 成功加载base64编码的应用程序图标
2025-07-14 09:50:27,861 [INFO] 应用程序启动
2025-07-14 09:50:27,991 [INFO] 成功加载base64编码的应用程序图标
2025-07-14 09:50:27,992 [INFO] 应用程序图标已设置
2025-07-14 09:50:27,994 [INFO] 成功加载base64编码的应用程序图标
2025-07-14 14:36:53,140 [INFO] 应用程序启动
2025-07-14 14:36:53,312 [INFO] 成功加载base64编码的应用程序图标
2025-07-14 14:36:53,313 [INFO] 应用程序图标已设置
2025-07-14 14:36:53,315 [INFO] 成功加载base64编码的应用程序图标
2025-07-14 14:37:05,165 [INFO] 成功加载base64编码的应用程序图标
2025-07-14 14:37:05,170 [INFO] 开始加载缓存文件: company_cache.dat
2025-07-14 14:37:05,187 [INFO] 缓存文件包含 2 个条目
2025-07-14 14:37:05,191 [INFO] 处理缓存条目: '联储证券股份有限公司'
2025-07-14 14:37:05,192 [INFO] 加载新格式缓存: '联储证券股份有限公司'
2025-07-14 14:37:05,193 [INFO] 处理缓存条目: '北京远翰国际教育咨询有限责任公司'
2025-07-14 14:37:05,194 [INFO] 加载新格式缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 14:37:05,194 [INFO] 缓存加载统计: 总计 2 个条目 (新格式: 2, 旧格式: 0, 错误: 0)
2025-07-14 14:37:05,194 [INFO] 缓存加载完成，共 2 个查询条目
2025-07-14 14:37:05,195 [INFO] 可用的缓存键示例: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 14:37:05,217 [INFO] 开始加载失信人缓存文件: dishonest_cache.dat
2025-07-14 14:37:05,221 [INFO] 失信人缓存文件包含 2 个条目
2025-07-14 14:37:05,222 [INFO] 加载失信人缓存: '联储证券股份有限公司'
2025-07-14 14:37:05,224 [INFO] 加载失信人缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 14:37:05,224 [INFO] 失信人缓存加载完成，共 2 个查询条目
2025-07-14 14:37:22,471 [INFO] Batch query initiated with keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 14:37:22,471 [INFO] Starting batch query for 2 keywords
2025-07-14 14:37:22,471 [INFO] Signal connections established
2025-07-14 14:37:22,472 [INFO] Starting worker thread...
2025-07-14 14:37:22,473 [INFO] DishonestQueryWorker.run() started with 2 keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 14:37:22,473 [INFO] No cache for keyword, will query API: 联储证券股份有限公司
2025-07-14 14:37:22,473 [INFO] No cache for keyword, will query API: 北京远翰国际教育咨询有限责任公司
2025-07-14 14:37:22,474 [INFO] Prepared 2 queries for processing
2025-07-14 14:37:22,474 [INFO] Starting query processing...
2025-07-14 14:37:22,474 [INFO] _process_next_query called: is_running=True, current_index=0, pending_count=2
2025-07-14 14:37:22,474 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 14:37:22,474 [INFO] Processing query 1/2: 联储证券股份有限公司
2025-07-14 14:37:22,474 [INFO] No cache for 联储证券股份有限公司, executing API query
2025-07-14 14:37:22,693 [INFO] Moving to next query: index now 1/2
2025-07-14 14:37:22,693 [INFO] 开始缓存失信人查询结果，查询字符串: '联储证券股份有限公司'
2025-07-14 14:37:22,693 [INFO] 使用失信人缓存键: '联储证券股份有限公司'
2025-07-14 14:37:22,700 [INFO] 已保存到失信人持久化缓存: '联储证券股份有限公司'
2025-07-14 14:37:22,732 [INFO] 已添加到失信人内存缓存: '联储证券股份有限公司'
2025-07-14 14:37:22,734 [INFO] ✅ 失信人查询成功: '联储证券股份有限公司' (来源: API)
2025-07-14 14:37:23,710 [INFO] _process_next_query called: is_running=True, current_index=1, pending_count=2
2025-07-14 14:37:23,710 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 14:37:23,710 [INFO] Processing query 2/2: 北京远翰国际教育咨询有限责任公司
2025-07-14 14:37:23,710 [INFO] No cache for 北京远翰国际教育咨询有限责任公司, executing API query
2025-07-14 14:37:23,968 [INFO] 开始缓存失信人查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-14 14:37:23,968 [INFO] 使用失信人缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-14 14:37:23,977 [INFO] 已保存到失信人持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 14:37:24,010 [INFO] 已添加到失信人内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 14:37:24,011 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: False
2025-07-14 14:37:24,032 [INFO] 显示结果: '北京远翰国际教育咨询有限责任公司', 缓存状态: False
2025-07-14 14:37:24,040 [INFO] ✅ 失信人查询成功: '北京远翰国际教育咨询有限责任公司' (来源: API)
2025-07-14 14:37:24,040 [INFO] Moving to next query: index now 2/2
2025-07-14 14:37:24,040 [INFO] All queries completed, will finish on next _process_next_query call
2025-07-14 14:37:24,041 [INFO] _process_next_query called: is_running=True, current_index=2, pending_count=2
2025-07-14 14:37:24,041 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 14:37:24,041 [INFO] All queries processed, emitting batch_completed signal and quitting event loop
2025-07-14 14:37:24,041 [INFO] Batch query finished handler called (custom signal)
2025-07-14 14:37:24,042 [INFO] Batch query statistics: successful=2, failed=0, total=2
2025-07-14 14:37:26,255 [INFO] 批量查询完成 - 成功: 2, 失败: 0
2025-07-14 14:37:27,558 [INFO] Batch query initiated with keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 14:37:27,558 [INFO] Starting batch query for 2 keywords
2025-07-14 14:37:27,558 [INFO] Signal connections established
2025-07-14 14:37:27,559 [INFO] Starting worker thread...
2025-07-14 14:37:27,559 [INFO] DishonestQueryWorker.run() started with 2 keywords: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 14:37:27,560 [INFO] Found cached data for keyword: 联储证券股份有限公司
2025-07-14 14:37:27,560 [INFO] Found cached data for keyword: 北京远翰国际教育咨询有限责任公司
2025-07-14 14:37:27,560 [INFO] Prepared 2 queries for processing
2025-07-14 14:37:27,561 [INFO] Starting query processing...
2025-07-14 14:37:27,561 [INFO] _process_next_query called: is_running=True, current_index=0, pending_count=2
2025-07-14 14:37:27,561 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 14:37:27,562 [INFO] Processing query 1/2: 联储证券股份有限公司
2025-07-14 14:37:27,562 [INFO] Found cache for 联储证券股份有限公司, prompting user
2025-07-14 14:37:29,585 [INFO] ✅ 失信人查询成功: '联储证券股份有限公司' (来源: 缓存)
2025-07-14 14:37:29,586 [INFO] Moving to next query: index now 1/2
2025-07-14 14:37:30,596 [INFO] _process_next_query called: is_running=True, current_index=1, pending_count=2
2025-07-14 14:37:30,597 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 14:37:30,597 [INFO] Processing query 2/2: 北京远翰国际教育咨询有限责任公司
2025-07-14 14:37:30,598 [INFO] Found cache for 北京远翰国际教育咨询有限责任公司, prompting user
2025-07-14 14:37:32,102 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-14 14:37:32,110 [INFO] 显示结果: '北京远翰国际教育咨询有限责任公司', 缓存状态: True
2025-07-14 14:37:32,119 [INFO] ✅ 失信人查询成功: '北京远翰国际教育咨询有限责任公司' (来源: 缓存)
2025-07-14 14:37:32,121 [INFO] Moving to next query: index now 2/2
2025-07-14 14:37:32,121 [INFO] All queries completed, will finish on next _process_next_query call
2025-07-14 14:37:32,121 [INFO] _process_next_query called: is_running=True, current_index=2, pending_count=2
2025-07-14 14:37:32,121 [INFO] Pending queries: ['联储证券股份有限公司', '北京远翰国际教育咨询有限责任公司']
2025-07-14 14:37:32,122 [INFO] All queries processed, emitting batch_completed signal and quitting event loop
2025-07-14 14:37:32,122 [INFO] Batch query finished handler called (custom signal)
2025-07-14 14:37:32,122 [INFO] Batch query statistics: successful=2, failed=0, total=2
2025-07-14 14:37:33,283 [INFO] 批量查询完成 - 成功: 2, 失败: 0
2025-07-14 14:41:40,201 [INFO] 开始缓存查询结果，查询字符串: '联储证券股份有限公司'
2025-07-14 14:41:40,202 [INFO] 使用缓存键: '联储证券股份有限公司'
2025-07-14 14:41:40,211 [INFO] 已保存到持久化缓存: '联储证券股份有限公司'
2025-07-14 14:41:40,234 [INFO] 已添加到内存缓存: '联储证券股份有限公司'
2025-07-14 14:41:40,234 [INFO] ✅ 缓存完成: 查询'联储证券股份有限公司' -> 企业(信用代码: 91440300727132290A)
2025-07-14 14:41:40,238 [INFO] ✅ 验证成功: '联储证券股份有限公司' 已存在于持久化缓存中
2025-07-14 14:41:41,468 [INFO] 开始缓存查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-14 14:41:41,468 [INFO] 使用缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-14 14:41:41,470 [INFO] 已保存到持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 14:41:41,486 [INFO] 已添加到内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-14 14:41:41,486 [INFO] ✅ 缓存完成: 查询'北京远翰国际教育咨询有限责任公司' -> 企业(信用代码: 91110107551400801W)
2025-07-14 14:41:41,489 [INFO] ✅ 验证成功: '北京远翰国际教育咨询有限责任公司' 已存在于持久化缓存中
2025-07-14 14:42:07,600 [INFO] 应用程序退出，退出代码：0
