"""定时检查设置对话框"""
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                             QLabel, QLineEdit, QComboBox, QTimeEdit, QDateEdit,
                             QSpinBox, QCheckBox, QDialogButtonBox, QTableWidget,
                             QTableWidgetItem, QHeaderView, QWidget, QMessageBox,
                             QTextEdit, QGroupBox, QSplitter)
from PyQt5.QtCore import Qt, QTimer, QTime, QDate
from PyQt5.QtGui import QFont
from scheduler_manager import SchedulerManager, ScheduleTask

import sys
import threading
import time
from datetime import datetime

class ScheduleDialog(QDialog):
    """调度设置对话框"""
    def __init__(self, scheduler_manager=None, parent=None):
        super().__init__(parent)
        self.scheduler_manager = scheduler_manager or SchedulerManager()
        self.setup_ui()
        self.refresh_task_list()
        # 启动定时刷新任务状态
        self.status_timer = QTimer(self)
        self.status_timer.timeout.connect(self.refresh_task_list)
        self.status_timer.start(5000)  # 每5秒刷新一次

    def closeEvent(self, event):
        """关闭对话框时的处理"""
        self.status_timer.stop()
        super().closeEvent(event)

    def setup_ui(self):
        self.setWindowTitle("定时检查设置")
        self.setMinimumWidth(800)
        layout = QVBoxLayout(self)

        # 分割布局
        splitter = QSplitter(Qt.Horizontal)

        # 左侧任务列表面板
        task_panel = QWidget()
        task_layout = QVBoxLayout(task_panel)

        # 任务列表
        self.task_list = QTableWidget()
        self.task_list.setColumnCount(6)
        self.task_list.setHorizontalHeaderLabels(["关键词", "时间", "频率", "状态", "上次执行", "下次执行"])
        self.task_list.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.task_list.itemSelectionChanged.connect(self.on_task_selected)
        task_layout.addWidget(self.task_list)

        # 控制按钮
        btn_layout = QHBoxLayout()
        self.add_btn = QPushButton("添加", self)
        self.edit_btn = QPushButton("编辑", self)
        self.delete_btn = QPushButton("删除", self)
        self.add_btn.clicked.connect(self.add_task)
        self.edit_btn.clicked.connect(self.edit_task)
        self.delete_btn.clicked.connect(self.delete_task)
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.edit_btn)
        btn_layout.addWidget(self.delete_btn)

        # 添加定时检查历史相关按钮
        history_btn = QPushButton("查看检查历史", self)
        history_btn.clicked.connect(self.view_check_history)
        clear_history_btn = QPushButton("清除检查历史", self)
        clear_history_btn.clicked.connect(self.clear_check_history)
        btn_layout.addWidget(history_btn)
        btn_layout.addWidget(clear_history_btn)

        task_layout.addLayout(btn_layout)

        # 右侧详情面板
        detail_panel = QWidget()
        detail_layout = QVBoxLayout(detail_panel)

        # 任务详情
        detail_group = QGroupBox("执行详情")
        detail_group_layout = QVBoxLayout()
        self.detail_text = QTextEdit()
        self.detail_text.setReadOnly(True)
        detail_group_layout.addWidget(self.detail_text)
        detail_group.setLayout(detail_group_layout)
        detail_layout.addWidget(detail_group)

        # 历史记录
        history_group = QGroupBox("当前任务历史记录")
        history_group_layout = QVBoxLayout()

        # 添加说明标签
        history_info_label = QLabel("显示当前选中任务的最近检查记录")
        history_info_label.setStyleSheet("color: #666; font-size: 11px; margin-bottom: 5px;")
        history_group_layout.addWidget(history_info_label)

        self.history_text = QTextEdit()
        self.history_text.setReadOnly(True)
        history_group_layout.addWidget(self.history_text)
        history_group.setLayout(history_group_layout)
        detail_layout.addWidget(history_group)

        # 添加到分割器
        splitter.addWidget(task_panel)
        splitter.addWidget(detail_panel)
        splitter.setSizes([400, 400])  # 设置初始宽度

        layout.addWidget(splitter)

    def refresh_task_list(self):
        """刷新任务列表"""
        self.task_list.setRowCount(len(self.scheduler_manager.tasks))
        for row, task in enumerate(self.scheduler_manager.tasks):
            # 关键词
            keywords = QTableWidgetItem(", ".join(task.keywords))
            self.task_list.setItem(row, 0, keywords)

            # 执行时间
            time_text = task.schedule_time
            if task.frequency == 'weekly' and task.days_of_week:
                weekdays = ['一', '二', '三', '四', '五', '六', '日']
                days = [weekdays[d-1] for d in task.days_of_week]
                time_text = f"{time_text} (每周{','.join(days)})"
            elif task.frequency == 'monthly' and task.day_of_month:
                time_text = f"{time_text} (每月{task.day_of_month}日)"
            elif task.frequency == 'once' and task.target_date:
                time_text = f"{task.target_date} {time_text}"
            time_item = QTableWidgetItem(time_text)
            self.task_list.setItem(row, 1, time_item)

            # 频率
            frequency_map = {
                'once': '一次',
                'daily': '每天',
                'weekly': '每周',
                'monthly': '每月'
            }
            frequency = QTableWidgetItem(frequency_map.get(task.frequency, '未知'))
            self.task_list.setItem(row, 2, frequency)

            # 状态信息
            status = self.scheduler_manager.get_task_status(task)
            status_item = QTableWidgetItem(status['status'])
            self.task_list.setItem(row, 3, status_item)

            # 上次执行
            last_run = QTableWidgetItem(task.last_run if task.last_run else '-')
            self.task_list.setItem(row, 4, last_run)

            # 下次执行
            next_run = QTableWidgetItem(task.next_run if task.next_run else '-')
            self.task_list.setItem(row, 5, next_run)

        self.task_list.resizeColumnsToContents()

    def on_task_selected(self):
        """当选中任务时更新详情显示"""
        current_row = self.task_list.currentRow()
        if current_row >= 0:
            task = self.scheduler_manager.tasks[current_row]

            # 更新详情显示
            status = self.scheduler_manager.get_task_status(task)
            details = [
                f"任务状态: {status['status']}",
                f"上次执行: {status['last_run'] or '从未执行'}",
                f"下次执行: {status['next_run'] or '未计划'}",
                "",
                "执行详情:",
                status.get('details', '暂无详情')
            ]
            self.detail_text.setPlainText('\n'.join(details))

            # 更新历史记录 - 显示当前任务的检查记录
            check_results = self.scheduler_manager.get_task_check_results(task)
            if check_results:
                history_text = []
                # 只显示最近5条记录，避免界面过于拥挤
                recent_results = check_results[:5]
                for record in recent_results:
                    history_text.append(f"时间: {record['execution_time']}")
                    history_text.append(f"状态: {record['status']}")

                    if record['status'] == 'success':
                        changes_count = len(record.get('changes_summary', []))
                        if changes_count > 0:
                            history_text.append(f"变更: 发现 {changes_count} 处变更")
                        else:
                            # 检查是否为首次检查
                            is_first_check = any(
                                result.get('is_first_check', False)
                                for result in record.get('results', {}).values()
                            )
                            if is_first_check:
                                history_text.append("状态: 首次检查完成")
                            else:
                                history_text.append("状态: 无变更")
                    else:
                        error_msg = record.get('error_message', '未知错误')
                        history_text.append(f"错误: {error_msg}")

                    history_text.append("")

                if len(check_results) > 5:
                    history_text.append(f"... 还有 {len(check_results) - 5} 条历史记录")
                    history_text.append('点击"查看检查历史"查看完整记录')

                self.history_text.setPlainText('\n'.join(history_text))
            else:
                self.history_text.setPlainText("暂无检查记录\n该任务尚未执行过定时检查")

    def add_task(self):
        """添加任务"""
        dialog = TaskEditDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.scheduler_manager.add_task(dialog.get_task())
            self.refresh_task_list()

    def edit_task(self):
        """编辑任务"""
        current_row = self.task_list.currentRow()
        if current_row >= 0:
            task = self.scheduler_manager.tasks[current_row]
            dialog = TaskEditDialog(self, task)
            if dialog.exec_() == QDialog.Accepted:
                self.scheduler_manager.update_task(current_row, dialog.get_task())
                self.refresh_task_list()

    def delete_task(self):
        """删除任务"""
        current_row = self.task_list.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(self, '确认', '确定要删除这个任务吗？',
                                      QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.scheduler_manager.remove_task(current_row)
                self.refresh_task_list()

    def view_check_history(self):
        """查看定时检查历史"""
        dialog = CheckHistoryDialog(self.scheduler_manager, self)
        dialog.exec_()

    def clear_check_history(self):
        """清除定时检查历史"""
        reply = QMessageBox.question(self, '确认清除',
                                   '确定要清除所有定时检查历史记录吗？\n此操作不可撤销！',
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            if self.scheduler_manager.clear_all_check_history():
                QMessageBox.information(self, '成功', '定时检查历史已清除')
                self.refresh_task_list()  # 刷新显示
            else:
                QMessageBox.warning(self, '错误', '清除定时检查历史失败')

class TaskEditDialog(QDialog):
    """任务编辑对话框"""
    def __init__(self, parent=None, task=None):
        super().__init__(parent)
        self.task = task
        self.setup_ui()
        if task:
            self.load_task(task)

    def setup_ui(self):
        self.setWindowTitle("编辑任务")
        layout = QVBoxLayout(self)

        # 关键词输入
        keywords_layout = QHBoxLayout()
        keywords_layout.addWidget(QLabel("关键词:"))
        self.keywords_edit = QLineEdit()
        self.keywords_edit.setPlaceholderText("多个关键词用逗号分隔")
        keywords_layout.addWidget(self.keywords_edit)
        layout.addLayout(keywords_layout)

        # 频率选择
        frequency_layout = QHBoxLayout()
        frequency_layout.addWidget(QLabel("频率:"))
        self.frequency_combo = QComboBox()
        self.frequency_combo.addItems(['一次', '每天', '每周', '每月'])
        self.frequency_combo.currentTextChanged.connect(self.on_frequency_changed)
        frequency_layout.addWidget(self.frequency_combo)
        layout.addLayout(frequency_layout)

        # 时间选择
        time_layout = QHBoxLayout()
        time_layout.addWidget(QLabel("执行时间:"))
        self.time_edit = QTimeEdit()
        self.time_edit.setDisplayFormat("HH:mm")
        time_layout.addWidget(self.time_edit)
        layout.addLayout(time_layout)

        # 具体日期选择（一次性任务）
        self.date_widget = QWidget()
        date_layout = QHBoxLayout(self.date_widget)
        date_layout.addWidget(QLabel("日期:"))
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        date_layout.addWidget(self.date_edit)
        layout.addWidget(self.date_widget)

        # 星期选择（每周任务）
        self.week_widget = QWidget()
        week_layout = QHBoxLayout(self.week_widget)
        week_layout.addWidget(QLabel("星期:"))
        self.week_checks = []
        for i, day in enumerate(['一', '二', '三', '四', '五', '六', '日']):
            check = QCheckBox(day)
            self.week_checks.append(check)
            week_layout.addWidget(check)
        layout.addWidget(self.week_widget)

        # 日期选择（每月任务）
        self.month_widget = QWidget()
        month_layout = QHBoxLayout(self.month_widget)
        month_layout.addWidget(QLabel("日期:"))
        self.month_spin = QSpinBox()
        self.month_spin.setRange(1, 31)
        month_layout.addWidget(self.month_spin)
        month_layout.addWidget(QLabel("日"))
        layout.addWidget(self.month_widget)

        # 启用复选框
        self.enabled_check = QCheckBox("启用")
        self.enabled_check.setChecked(True)
        layout.addWidget(self.enabled_check)

        # 按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, self)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

        # 初始化UI状态 - 修复问题1：确保默认频率"一次"时显示日期选择器
        self.update_ui_visibility()

    def update_ui_visibility(self):
        """根据当前频率设置更新UI控件的可见性"""
        current_frequency = self.frequency_combo.currentText()
        self.date_widget.setVisible(current_frequency == '一次')
        self.week_widget.setVisible(current_frequency == '每周')
        self.month_widget.setVisible(current_frequency == '每月')

    def on_frequency_changed(self, text):
        """频率改变时更新UI"""
        self.update_ui_visibility()

    def load_task(self, task):
        """加载任务数据"""
        self.keywords_edit.setText(", ".join(task.keywords))
        self.time_edit.setTime(QTime.fromString(task.schedule_time, "HH:mm"))
        self.enabled_check.setChecked(task.enabled)

        # 设置频率
        frequency_map = {
            'once': '一次',
            'daily': '每天',
            'weekly': '每周',
            'monthly': '每月'
        }
        index = self.frequency_combo.findText(frequency_map.get(task.frequency, '一次'))
        self.frequency_combo.setCurrentIndex(index)

        # 设置具体日期（一次性任务）
        if task.target_date:
            self.date_edit.setDate(QDate.fromString(task.target_date, "yyyy-MM-dd"))

        # 设置星期（每周任务）
        if task.days_of_week:
            for day in task.days_of_week:
                if 0 <= day-1 < len(self.week_checks):
                    self.week_checks[day-1].setChecked(True)

        # 设置日期（每月任务）
        if task.day_of_month:
            self.month_spin.setValue(task.day_of_month)

        # 更新UI可见性 - 确保加载任务后UI状态正确
        self.update_ui_visibility()

    def get_task(self) -> ScheduleTask:
        """获取任务数据"""
        keywords = [k.strip() for k in self.keywords_edit.text().split(",") if k.strip()]
        frequency_map = {
            '一次': 'once',
            '每天': 'daily',
            '每周': 'weekly',
            '每月': 'monthly'
        }
        frequency = frequency_map.get(self.frequency_combo.currentText(), 'once')

        # 获取时间
        time_str = self.time_edit.time().toString("HH:mm")

        # 创建任务对象
        task = ScheduleTask(
            keywords=keywords,
            schedule_time=time_str,
            frequency=frequency,
            enabled=self.enabled_check.isChecked()
        )

        # 设置具体日期（一次性任务）
        if frequency == 'once':
            task.target_date = self.date_edit.date().toString("yyyy-MM-dd")

        # 设置星期（每周任务）
        elif frequency == 'weekly':
            task.days_of_week = [i+1 for i, check in enumerate(self.week_checks) if check.isChecked()]

        # 设置日期（每月任务）
        elif frequency == 'monthly':
            task.day_of_month = self.month_spin.value()

        return task


class CheckHistoryDialog(QDialog):
    """定时检查历史查看对话框"""
    def __init__(self, scheduler_manager, parent=None):
        super().__init__(parent)
        self.scheduler_manager = scheduler_manager
        self.setup_ui()
        self.load_history()

    def setup_ui(self):
        self.setWindowTitle("定时检查历史")
        self.setMinimumSize(900, 600)
        layout = QVBoxLayout(self)

        # 历史记录列表
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(5)
        self.history_table.setHorizontalHeaderLabels(["执行时间", "任务关键词", "状态", "变更数量", "详情"])
        self.history_table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.history_table.itemSelectionChanged.connect(self.on_history_selected)
        layout.addWidget(self.history_table)

        # 详情显示区域
        detail_group = QGroupBox("检查详情")
        detail_layout = QVBoxLayout()
        self.detail_text = QTextEdit()
        self.detail_text.setReadOnly(True)
        detail_layout.addWidget(self.detail_text)
        detail_group.setLayout(detail_layout)
        layout.addWidget(detail_group)

        # 按钮布局
        button_layout = QHBoxLayout()

        # 添加隐藏的JSON查看按钮（小尺寸）
        json_btn = QPushButton('🔍')
        json_btn.setMaximumWidth(30)
        json_btn.setMaximumHeight(25)
        json_btn.setToolTip('查看完整JSON响应（调试用）')
        json_btn.clicked.connect(self.show_json_response)

        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.load_history)
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)

        button_layout.addWidget(json_btn)
        button_layout.addStretch()
        button_layout.addWidget(refresh_btn)
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)

    def load_history(self):
        """加载检查历史"""
        all_results = self.scheduler_manager.get_all_check_results()

        # 收集所有检查记录
        history_records = []
        for task_id, records in all_results.items():
            for record in records:
                history_records.append({
                    'task_id': task_id,
                    'record': record
                })

        # 按时间排序
        history_records.sort(key=lambda x: x['record']['execution_time'], reverse=True)

        # 填充表格
        self.history_table.setRowCount(len(history_records))
        for row, item in enumerate(history_records):
            record = item['record']

            # 执行时间
            time_item = QTableWidgetItem(record['execution_time'])
            self.history_table.setItem(row, 0, time_item)

            # 任务关键词
            keywords = record['task_info']['keywords']
            keywords_item = QTableWidgetItem(', '.join(keywords))
            self.history_table.setItem(row, 1, keywords_item)

            # 状态
            status_item = QTableWidgetItem(record['status'])
            self.history_table.setItem(row, 2, status_item)

            # 变更数量
            changes_count = len(record.get('changes_summary', []))
            changes_item = QTableWidgetItem(str(changes_count))
            self.history_table.setItem(row, 3, changes_item)

            # 详情预览
            if record['status'] == 'success':
                if changes_count > 0:
                    detail = f"发现 {changes_count} 处变更"
                else:
                    detail = "无变更"
            else:
                detail = record.get('error_message', '执行出错')
            detail_item = QTableWidgetItem(detail)
            self.history_table.setItem(row, 4, detail_item)

        self.history_table.resizeColumnsToContents()

    def on_history_selected(self):
        """当选中历史记录时显示详情"""
        current_row = self.history_table.currentRow()
        if current_row >= 0:
            all_results = self.scheduler_manager.get_all_check_results()

            # 重新构建记录列表以获取选中的记录
            history_records = []
            for task_id, records in all_results.items():
                for record in records:
                    history_records.append({
                        'task_id': task_id,
                        'record': record
                    })

            history_records.sort(key=lambda x: x['record']['execution_time'], reverse=True)

            if current_row < len(history_records):
                selected_item = history_records[current_row]
                record = selected_item['record']

                # 构建详情文本
                details = []
                details.append(f"执行时间: {record['execution_time']}")
                details.append(f"任务ID: {selected_item['task_id']}")
                details.append(f"状态: {record['status']}")
                details.append("")

                if record['status'] == 'success':
                    details.append("查询结果:")
                    for keyword, result in record['results'].items():
                        changes = result.get('changes', [])
                        if changes:
                            details.append(f"  {keyword}: 发现 {len(changes)} 处变更")
                            for change in changes:
                                details.append(f"    - {change}")
                        else:
                            details.append(f"  {keyword}: 无变更")
                else:
                    details.append(f"错误信息: {record.get('error_message', '未知错误')}")

                self.detail_text.setPlainText('\n'.join(details))

    def show_json_response(self):
        """显示完整JSON响应（隐藏功能）"""
        current_row = self.history_table.currentRow()
        if current_row >= 0:
            all_results = self.scheduler_manager.get_all_check_results()

            # 重新构建记录列表
            history_records = []
            for task_id, records in all_results.items():
                for record in records:
                    history_records.append({
                        'task_id': task_id,
                        'record': record
                    })

            history_records.sort(key=lambda x: x['record']['execution_time'], reverse=True)

            if current_row < len(history_records):
                selected_item = history_records[current_row]
                record = selected_item['record']

                # 创建JSON显示对话框
                json_dialog = QDialog(self)
                json_dialog.setWindowTitle('完整JSON响应 - 调试')
                json_dialog.setGeometry(200, 200, 800, 600)

                layout = QVBoxLayout()

                info_label = QLabel("以下是完整的API响应JSON数据：")
                layout.addWidget(info_label)

                json_display = QTextEdit()
                json_display.setReadOnly(True)
                json_display.setFont(QFont("Consolas", 10))

                # 格式化JSON数据
                import json
                formatted_json = json.dumps(record, ensure_ascii=False, indent=2)
                json_display.setText(formatted_json)

                layout.addWidget(json_display)

                # 复制按钮
                copy_btn = QPushButton('复制到剪贴板')
                copy_btn.clicked.connect(lambda: self.copy_to_clipboard(formatted_json))
                layout.addWidget(copy_btn)

                json_dialog.setLayout(layout)
                json_dialog.exec_()

    def copy_to_clipboard(self, text):
        """复制文本到剪贴板"""
        from PyQt5.QtWidgets import QApplication
        clipboard = QApplication.clipboard()
        clipboard.setText(text)
        QMessageBox.information(self, '成功', '已复制到剪贴板')
