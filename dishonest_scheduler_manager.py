"""失信被执行人定时检查管理模块"""
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import os
import calendar
from typing import Dict, List, Optional
import threading
import time
import logging


@dataclass
class DishonestScheduleTask:
    """失信人定时任务"""
    keywords: List[str]      # 要查询的关键词列表
    schedule_time: str       # 计划执行时间 HH:MM 格式
    frequency: str           # 执行频率：once(一次), daily(每天), weekly(每周), monthly(每月)
    enabled: bool = True     # 是否启用
    last_run: str = ""      # 上次执行时间
    target_date: str = ""   # 目标执行日期 YYYY-MM-DD格式（用于一次性任务）
    days_of_week: List[int] = None  # 每周执行的星期几 [0-6]（用于每周任务）
    day_of_month: int = None        # 每月执行的日期 1-31（用于每月任务）
    next_run: str = ""      # 下次执行时间 YYYY-MM-DD HH:MM格式

    def to_dict(self) -> Dict:
        return {
            'keywords': self.keywords,
            'schedule_time': self.schedule_time,
            'frequency': self.frequency,
            'enabled': self.enabled,
            'last_run': self.last_run,
            'target_date': self.target_date,
            'days_of_week': self.days_of_week,
            'day_of_month': self.day_of_month,
            'next_run': self.next_run
        }

    @classmethod
    def from_dict(cls, data: Dict) -> 'DishonestScheduleTask':
        return cls(
            keywords=data.get('keywords', []),
            schedule_time=data.get('schedule_time', ''),
            frequency=data.get('frequency', 'once'),
            enabled=data.get('enabled', True),
            last_run=data.get('last_run', ''),
            target_date=data.get('target_date', ''),
            days_of_week=data.get('days_of_week', None),
            day_of_month=data.get('day_of_month', None),
            next_run=data.get('next_run', '')
        )

    def calculate_next_run(self) -> str:
        """计算下次执行时间"""
        current_time = datetime.now()
        time_parts = self.schedule_time.split(':')
        hour, minute = int(time_parts[0]), int(time_parts[1])

        if self.frequency == 'once':
            if not self.target_date:
                return ''
            target = datetime.strptime(f"{self.target_date} {self.schedule_time}", "%Y-%m-%d %H:%M")
            if target > current_time:
                return target.strftime("%Y-%m-%d %H:%M")
            return ''

        elif self.frequency == 'daily':
            next_run = current_time.replace(hour=hour, minute=minute, second=0, microsecond=0)
            if next_run <= current_time:
                next_run = next_run + timedelta(days=1)
            return next_run.strftime("%Y-%m-%d %H:%M")

        elif self.frequency == 'weekly':
            if not self.days_of_week:
                return ''

            next_run = current_time.replace(hour=hour, minute=minute, second=0, microsecond=0)
            current_weekday = current_time.weekday()

            # 找到下一个执行日
            days_ahead = None
            for day in sorted(self.days_of_week):
                if day > current_weekday or (day == current_weekday and next_run > current_time):
                    days_ahead = day - current_weekday
                    break

            if days_ahead is None:
                days_ahead = 7 - current_weekday + min(self.days_of_week)

            next_run = next_run + timedelta(days=days_ahead)
            return next_run.strftime("%Y-%m-%d %H:%M")

        elif self.frequency == 'monthly':
            if not self.day_of_month:
                return ''

            next_run = current_time.replace(hour=hour, minute=minute, second=0, microsecond=0)

            # 尝试当月
            try:
                next_run = next_run.replace(day=self.day_of_month)
                if next_run <= current_time:
                    # 下个月
                    if next_run.month == 12:
                        next_run = next_run.replace(year=next_run.year + 1, month=1)
                    else:
                        next_run = next_run.replace(month=next_run.month + 1)

                    # 处理月末日期
                    max_day = calendar.monthrange(next_run.year, next_run.month)[1]
                    if self.day_of_month > max_day:
                        next_run = next_run.replace(day=max_day)
                    else:
                        next_run = next_run.replace(day=self.day_of_month)
            except ValueError:
                # 处理无效日期（如2月30日）
                max_day = calendar.monthrange(next_run.year, next_run.month)[1]
                next_run = next_run.replace(day=max_day)

            return next_run.strftime("%Y-%m-%d %H:%M")

        return ''


class DishonestSchedulerManager:
    """失信人调度管理器"""
    def __init__(self, api_client=None):
        self.api_client = api_client
        self.tasks: List[DishonestScheduleTask] = []
        self.running = False
        self.thread = None
        self.task_status = {}  # 存储任务执行状态
        self._status_lock = threading.Lock()
        self.config_file = "dishonest_schedule_config.json"
        self.history_file = "dishonest_schedule_history.json"
        self.check_results_file = "dishonest_schedule_check_results.json"
        self.keyword_check_results_file = "dishonest_keyword_check_results.json"
        self.check_results = {}  # 存储完整的定时检查结果
        self.keyword_check_results = {}  # 以关键词为索引的检查结果
        self.query_history = {}  # 查询历史
        self._load_tasks()
        self._load_history()
        self._load_check_results()
        self._load_keyword_check_results()
        self.start()  # 自动启动调度器

    def _load_tasks(self):
        """从文件加载任务配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.tasks = [DishonestScheduleTask.from_dict(task_data) for task_data in data]
                    # 更新所有任务的下次执行时间
                    for task in self.tasks:
                        if task.enabled:
                            task.next_run = task.calculate_next_run()
        except Exception as e:
            logging.error(f"加载失信人任务配置时出错: {e}")
            self.tasks = []

    def save_tasks(self):
        """保存任务配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump([task.to_dict() for task in self.tasks], f,
                         ensure_ascii=False, indent=2)
        except Exception as e:
            logging.error(f"保存失信人任务配置时出错: {e}")

    def _load_history(self):
        """加载查询历史"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    self.query_history = json.load(f)
            else:
                self.query_history = {}
        except Exception as e:
            logging.error(f"加载失信人查询历史时出错: {e}")
            self.query_history = {}

    def save_history(self):
        """保存查询历史"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.query_history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logging.error(f"保存失信人查询历史时出错: {e}")

    def _load_check_results(self):
        """加载定时检查结果"""
        try:
            if os.path.exists(self.check_results_file):
                with open(self.check_results_file, 'r', encoding='utf-8') as f:
                    self.check_results = json.load(f)
            else:
                self.check_results = {}
        except Exception as e:
            logging.error(f"加载失信人检查结果时出错: {e}")
            self.check_results = {}

    def save_check_results(self):
        """保存定时检查结果"""
        try:
            with open(self.check_results_file, 'w', encoding='utf-8') as f:
                json.dump(self.check_results, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logging.error(f"保存失信人检查结果时出错: {e}")

    def _load_keyword_check_results(self):
        """加载关键词检查结果"""
        try:
            if os.path.exists(self.keyword_check_results_file):
                with open(self.keyword_check_results_file, 'r', encoding='utf-8') as f:
                    self.keyword_check_results = json.load(f)
            else:
                self.keyword_check_results = {}
        except Exception as e:
            logging.error(f"加载失信人关键词检查结果时出错: {e}")
            self.keyword_check_results = {}

    def save_keyword_check_results(self):
        """保存关键词检查结果"""
        try:
            with open(self.keyword_check_results_file, 'w', encoding='utf-8') as f:
                json.dump(self.keyword_check_results, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logging.error(f"保存失信人关键词检查结果时出错: {e}")

    def _generate_task_id(self, task: DishonestScheduleTask) -> str:
        """为任务生成唯一ID"""
        keywords_str = ",".join(sorted(task.keywords))
        return f"dishonest_{keywords_str}_{task.frequency}_{task.schedule_time}"

    def add_task(self, task: DishonestScheduleTask) -> bool:
        """添加新任务"""
        if task.enabled:
            task.next_run = task.calculate_next_run()
        self.tasks.append(task)
        self.save_tasks()
        return True

    def update_task(self, index: int, task: DishonestScheduleTask) -> bool:
        """更新任务"""
        if 0 <= index < len(self.tasks):
            if task.enabled:
                task.next_run = task.calculate_next_run()
            self.tasks[index] = task
            self.save_tasks()
            return True
        return False

    def remove_task(self, index: int) -> bool:
        """删除任务"""
        if 0 <= index < len(self.tasks):
            self.tasks.pop(index)
            self.save_tasks()
            return True
        return False

    def get_task_status(self, task: DishonestScheduleTask) -> Dict[str, str]:
        """获取任务状态"""
        task_id = self._generate_task_id(task)
        with self._status_lock:
            return self.task_status.get(task_id, {'status': '等待中', 'details': ''})

    def _update_task_status(self, task: DishonestScheduleTask, status: str, details: str = ''):
        """更新任务状态"""
        task_id = self._generate_task_id(task)
        with self._status_lock:
            self.task_status[task_id] = {
                'status': status,
                'details': details,
                'update_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

    def start(self):
        """启动调度器"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self._run_scheduler)
            self.thread.daemon = True
            self.thread.start()

    def stop(self):
        """停止调度器"""
        self.running = False
        if self.thread:
            self.thread.join()
            self.thread = None

    def _compare_results(self, keyword: str, new_data: dict) -> List[str]:
        """比较新旧查询结果，返回变更列表"""
        changes = []

        # 使用关键词检查结果进行比较
        if keyword in self.keyword_check_results and self.keyword_check_results[keyword]:
            last_record = self.keyword_check_results[keyword][-1]
            last_data = last_record.get('api_response', {}).get('data', {})

            # 比较失信人数据
            if 'result' in new_data and 'result' in last_data:
                new_result = new_data['result']
                old_result = last_data['result']

                # 比较总数
                new_total = new_result.get('total', 0)
                old_total = old_result.get('total', 0)
                if new_total != old_total:
                    changes.append(f"失信记录总数变更: {old_total} -> {new_total}")

                # 比较失信记录
                new_items = new_result.get('items', [])
                old_items = old_result.get('items', [])

                if len(new_items) != len(old_items):
                    changes.append(f"失信记录条数变更: {len(old_items)} -> {len(new_items)}")

                # 简单比较：检查是否有新的案号
                new_cases = {item.get('casecode', '') for item in new_items if item.get('casecode')}
                old_cases = {item.get('casecode', '') for item in old_items if item.get('casecode')}

                added_cases = new_cases - old_cases
                removed_cases = old_cases - new_cases

                if added_cases:
                    changes.append(f"新增失信案件: {', '.join(added_cases)}")
                if removed_cases:
                    changes.append(f"移除失信案件: {', '.join(removed_cases)}")

        return changes

    def _run_scheduler(self):
        """运行调度器主循环"""
        while self.running:
            current_time = datetime.now()
            for task in self.tasks:
                if not task.enabled or not task.next_run:
                    continue

                next_run_time = datetime.strptime(task.next_run, "%Y-%m-%d %H:%M")
                if current_time >= next_run_time:
                    try:
                        self._update_task_status(task, '执行中', '正在查询失信人信息...')
                        task_id = self._generate_task_id(task)
                        execution_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
                        results = {}
                        changes_found = False

                        # 初始化任务检查结果记录
                        if task_id not in self.check_results:
                            self.check_results[task_id] = []

                        check_record = {
                            'execution_time': execution_time,
                            'task_info': {
                                'keywords': task.keywords,
                                'frequency': task.frequency,
                                'schedule_time': task.schedule_time
                            },
                            'results': {},
                            'status': 'success',
                            'error_message': None,
                            'changes_summary': []
                        }

                        # 检查哪些关键词是首次检查
                        first_check_keywords = []
                        for keyword in task.keywords:
                            if keyword not in self.keyword_check_results or not self.keyword_check_results[keyword]:
                                first_check_keywords.append(keyword)

                        # 逐个查询关键词（顺序执行）
                        for keyword in task.keywords:
                            if not self.api_client:
                                raise Exception("API客户端未初始化")

                            # 执行失信人查询
                            result = self.api_client.query_dishonest_person(keyword)
                            if not result['success']:
                                raise Exception(f"查询失信人失败: {result['message']}")

                            # 分析查询结果
                            analysis = self._analyze_dishonest_results(result['data'])

                            # 保存完整的API响应到检查结果
                            check_record['results'][keyword] = {
                                'api_response': result,
                                'query_time': execution_time,
                                'success': True,
                                'is_first_check': keyword in first_check_keywords,
                                'analysis': analysis  # 添加分析结果
                            }

                            # 比较结果
                            changes = self._compare_results(keyword, result['data'])
                            check_record['results'][keyword]['changes'] = changes

                            # 保存到关键词索引的检查结果
                            if keyword not in self.keyword_check_results:
                                self.keyword_check_results[keyword] = []

                            keyword_record = {
                                'execution_time': execution_time,
                                'api_response': result,
                                'changes': changes,
                                'task_id': task_id,
                                'analysis': analysis  # 添加分析结果
                            }
                            self.keyword_check_results[keyword].append(keyword_record)

                            # 限制每个关键词保存的历史记录数量
                            if len(self.keyword_check_results[keyword]) > 50:
                                self.keyword_check_results[keyword] = self.keyword_check_results[keyword][-50:]

                            results[keyword] = {
                                'changes': changes,
                                'is_first_check': keyword in first_check_keywords,
                                'analysis': analysis  # 添加分析结果
                            }

                            if changes:
                                changes_found = True
                                check_record['changes_summary'].extend([f"{keyword}: {change}" for change in changes])

                            # 添加延迟避免请求过快
                            time.sleep(1)

                        # 更新任务状态
                        task.last_run = current_time.strftime("%Y-%m-%d %H:%M")
                        task.next_run = task.calculate_next_run()
                        if task.frequency == 'once':
                            task.enabled = False

                        # 生成状态详情
                        details = []
                        first_check_count = len(first_check_keywords)

                        for kw, res in results.items():
                            analysis = res.get('analysis', {})

                            # 添加失信记录存在性和最新发布时间信息
                            if analysis.get('summary'):
                                details.append(f"{kw}: {analysis['summary']}")

                            # 添加变更信息
                            if res['changes']:
                                details.append(f"{kw}: 发现{len(res['changes'])}处变更")
                            else:
                                if res['is_first_check']:
                                    details.append(f"{kw}: 首次检查完成，已建立基准数据")
                                else:
                                    details.append(f"{kw}: 与上次查询结果相比，未发生变化")

                        # 根据检查类型确定状态
                        if changes_found:
                            status = '已完成(有变更)'
                        elif first_check_count == len(task.keywords):
                            status = '已完成(首次检查)'
                        elif first_check_count > 0:
                            status = '已完成(部分首次检查)'
                        else:
                            status = '已完成(无变更)'

                        check_record['status'] = 'success'

                        # 保存检查结果记录
                        self.check_results[task_id].append(check_record)

                        # 限制每个任务保存的历史记录数量
                        if len(self.check_results[task_id]) > 100:
                            self.check_results[task_id] = self.check_results[task_id][-100:]

                        self._update_task_status(task, status, '\n'.join(details))

                        # 保存历史记录和检查结果
                        self.save_history()
                        self.save_check_results()
                        self.save_keyword_check_results()

                    except Exception as e:
                        error_msg = str(e)
                        logging.error(f"执行失信人任务时出错: {error_msg}")

                        # 记录错误到检查结果
                        task_id = self._generate_task_id(task)
                        if task_id not in self.check_results:
                            self.check_results[task_id] = []

                        error_record = {
                            'execution_time': current_time.strftime("%Y-%m-%d %H:%M:%S"),
                            'task_info': {
                                'keywords': task.keywords,
                                'frequency': task.frequency,
                                'schedule_time': task.schedule_time
                            },
                            'results': {},
                            'status': 'error',
                            'error_message': error_msg,
                            'changes_summary': []
                        }
                        self.check_results[task_id].append(error_record)

                        # 更新任务的下次执行时间（即使出错也要继续调度）
                        task.last_run = current_time.strftime("%Y-%m-%d %H:%M")
                        task.next_run = task.calculate_next_run()
                        if task.frequency == 'once':
                            task.enabled = False

                        self.save_check_results()
                        self._update_task_status(task, '执行出错', error_msg)

            self.save_tasks()  # 保存任务状态
            time.sleep(60)  # 每分钟检查一次

    def get_task_history(self, task: DishonestScheduleTask) -> List[Dict]:
        """获取任务执行历史"""
        task_id = self._generate_task_id(task)
        return self.check_results.get(task_id, [])

    def _analyze_dishonest_results(self, data: dict) -> dict:
        """分析失信人查询结果，提供存在性检查和最新发布时间"""
        analysis = {
            'has_records': False,
            'total_count': 0,
            'latest_publish_time': None,
            'latest_publish_time_formatted': '',
            'summary': ''
        }

        try:
            if not data or 'result' not in data:
                analysis['summary'] = '无失信记录'
                return analysis

            result_data = data['result']
            items = result_data.get('items', [])
            total = result_data.get('total', 0)

            analysis['total_count'] = total
            analysis['has_records'] = total > 0 and len(items) > 0

            if analysis['has_records']:
                # 查找最新的发布时间
                latest_timestamp = 0
                for item in items:
                    publish_date = item.get('publishdate', 0)
                    if publish_date and publish_date > latest_timestamp:
                        latest_timestamp = publish_date

                if latest_timestamp > 0:
                    analysis['latest_publish_time'] = latest_timestamp
                    # 转换为可读格式 (YYYY-MM-DD HH:MM)
                    try:
                        # 处理毫秒级时间戳
                        if latest_timestamp > 10000000000:
                            latest_timestamp = latest_timestamp / 1000

                        from datetime import datetime
                        dt = datetime.fromtimestamp(latest_timestamp)
                        analysis['latest_publish_time_formatted'] = dt.strftime('%Y-%m-%d %H:%M')
                    except (ValueError, OSError):
                        analysis['latest_publish_time_formatted'] = str(latest_timestamp)

                # 生成摘要
                if total == 1:
                    analysis['summary'] = f'存在失信记录，共1条，最新发布时间：{analysis["latest_publish_time_formatted"]}'
                else:
                    analysis['summary'] = f'存在失信记录，共{total}条，最新发布时间：{analysis["latest_publish_time_formatted"]}'
            else:
                analysis['summary'] = '无失信记录'

        except Exception as e:
            logging.error(f"分析失信人查询结果时出错: {e}")
            analysis['summary'] = f'分析失败: {str(e)}'

        return analysis

    def clear_all_check_history(self) -> bool:
        """清除所有定时检查历史记录"""
        try:
            self.check_results = {}
            self.keyword_check_results = {}
            self.query_history = {}

            self.save_check_results()
            self.save_keyword_check_results()
            self.save_history()

            logging.info("失信人定时检查历史已清除")
            return True

        except Exception as e:
            logging.error(f"清除失信人定时检查历史时出错: {e}")
            return False
