"""配置管理模块"""
from dataclasses import dataclass, field
from typing import Dict, Any
import json
import os

@dataclass
class DataDisplayConfig:
    """数据显示配置类"""
    sections: Dict[str, Any] = field(default_factory=dict)
    
    # 向后兼容的属性
    @property
    def show_branch_info(self) -> bool:
        if 'branch_info' not in self.sections:
            self.sections['branch_info'] = {'enabled': False}
        return bool(self.sections['branch_info'].get('enabled', False))
    
    @property
    def show_annual_reports(self) -> bool:
        if 'annual_reports' not in self.sections:
            self.sections['annual_reports'] = {'enabled': False}
        return bool(self.sections['annual_reports'].get('enabled', False))
    
    @property
    def show_judicial_assistance(self) -> bool:
        if 'judicial_assistance' not in self.sections:
            self.sections['judicial_assistance'] = {'enabled': False}
        return bool(self.sections['judicial_assistance'].get('enabled', False))
    
    @property
    def show_mortgage_info(self) -> bool:
        if 'mortgage_info' not in self.sections:
            self.sections['mortgage_info'] = {'enabled': False}
        return bool(self.sections['mortgage_info'].get('enabled', False))
    
    @property
    def show_ipr_pledge(self) -> bool:
        if 'ipr_pledge' not in self.sections:
            self.sections['ipr_pledge'] = {'enabled': False}
        return bool(self.sections['ipr_pledge'].get('enabled', False))
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'DataDisplayConfig':
        """从字典创建实例"""
        if 'sections' not in data:
            # 处理旧格式的配置
            sections = {
                'branch_info': {'enabled': data.get('show_branch_info', False)},
                'annual_reports': {'enabled': data.get('show_annual_reports', False)},
                'judicial_assistance': {'enabled': data.get('show_judicial_assistance', False)},
                'mortgage_info': {'enabled': data.get('show_mortgage_info', False)},
                'ipr_pledge': {'enabled': data.get('show_ipr_pledge', False)}
            }
            return cls(sections=sections)
        return cls(sections=data.get('sections', {}))

    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'sections': self.sections
        }

    def update_section_enabled(self, section_name: str, enabled: bool):
        """更新部分的启用状态"""
        if section_name not in self.sections:
            self.sections[section_name] = {}
        self.sections[section_name]['enabled'] = enabled

class ConfigManager:
    """配置管理器"""
    def __init__(self, config_file: str = 'display_config.json'):
        self.config_file = config_file
        self.display_config = self._load_config()

    def _load_config(self) -> DataDisplayConfig:
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return DataDisplayConfig.from_dict(data)
        except Exception as e:
            print(f"加载配置文件出错: {str(e)}")
        return self._create_default_config()

    def _create_default_config(self) -> DataDisplayConfig:
        """创建默认配置"""
        default_config = {
            'sections': {
                'basic_info': {
                    'title': '基本信息',
                    'enabled': True,
                    'fields': [
                        {'key': 'entName', 'display': '企业名称'},
                        {'key': 'unifiedCode', 'display': '统一社会信用代码'},
                        {'key': 'regNo', 'display': '注册号'},
                        {'key': 'entStatus', 'display': '登记状态'},
                        {'key': 'legalPerson', 'display': '法定代表人'},
                        {'key': 'regCapital', 'display': '注册资本'},
                        {'key': 'esDate', 'display': '成立日期'},
                        {'key': 'entType', 'display': '企业类型'},
                        {'key': 'scope', 'display': '经营范围'}
                    ]
                },
                'branch_info': {'enabled': False, 'title': '分支机构信息'},
                'annual_reports': {'enabled': False, 'title': '年报信息'},
                'judicial_assistance': {'enabled': False, 'title': '司法协助信息'},
                'mortgage_info': {'enabled': False, 'title': '动产抵押信息'},
                'ipr_pledge': {'enabled': False, 'title': '知识产权出质信息'}
            }
        }
        return DataDisplayConfig.from_dict(default_config)

    def save_config(self):
        """保存配置"""
        try:
            config_data = self.display_config.to_dict()
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件出错: {str(e)}")

    def update_config(self, new_config: DataDisplayConfig):
        """更新配置"""
        self.display_config = new_config
        self.save_config()
