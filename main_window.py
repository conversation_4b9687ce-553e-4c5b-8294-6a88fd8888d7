import os
import sys
from PyQt5.QtWidgets import (QMain<PERSON>indow, QWidget, QVBoxLayout, QHBoxLayout,
                           QLabel, QLineEdit, QPushButton, QTabWidget,
                           QTextEdit, QComboBox, QFileDialog, QMessageBox,
                           QProgressBar, QStatusBar, QCheckBox, QGroupBox, QDialog,
                           QGridLayout)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont
from api_client import APIClient
from data_processor import DataProcessor
from config_manager import ConfigManager, DataDisplayConfig
from scheduler_manager import SchedulerManager
from schedule_dialog import ScheduleDialog
from dishonest_data_processor import DishonestDataProcessor
from dishonest_scheduler_manager import DishonestSchedulerManager
from dishonest_schedule_dialog import DishonestScheduleDialog
from version import VERSION
from resource_utils import get_application_icon
import json
from datetime import datetime
from queue import Queue
import time
import shelve
import threading
import pandas as pd
import traceback
import pathlib
import logging

class QueryWorker(QThread):
    """查询工作线程"""
    progress_updated = pyqtSignal(int, int)  # 当前进度，总数
    query_completed = pyqtSignal(dict)  # 查询结果
    query_error = pyqtSignal(str)  # 错误信息
    status_updated = pyqtSignal(str)  # 状态更新

    def __init__(self, api_client, keywords):
        super().__init__()
        self.api_client = api_client
        self.keywords = keywords
        self.is_running = True

    def run(self):
        total = len(self.keywords)
        self.status_updated.emit(f"开始查询 {total} 个企业信息...")

        for i, keyword in enumerate(self.keywords, 1):
            if not self.is_running:
                self.status_updated.emit("查询已停止")
                break

            self.status_updated.emit(f"正在查询: {keyword}")
            result = self.api_client.query_company_info(keyword)

            self.progress_updated.emit(i, total)

            if result['success']:
                self.query_completed.emit({
                    'keyword': keyword,
                    'data': result['data']
                })
            else:
                self.query_error.emit(f"查询 {keyword} 失败: {result['message']}")

            # 避免请求过快
            if i < total:
                self.status_updated.emit("等待下一次查询...")
                time.sleep(1)

        if self.is_running:
            self.status_updated.emit("查询完成")

    def stop(self):
        self.is_running = False

class CompanyInfoTab(QWidget):
    def __init__(self, api_client: APIClient, config_manager: ConfigManager = None):
        super().__init__()
        self.api_client = api_client
        self.config_manager = config_manager or ConfigManager()
        self.data_processor = DataProcessor(self.config_manager.display_config)
        self.scheduler_manager = SchedulerManager(self.api_client)
        self.query_results = {}
        self.current_result = None
        self.query_worker = None
        self.cache_file = "company_cache"
        self.is_current_data_cached = False  # 跟踪当前显示的数据是否来自缓存
        self.load_cache()
        self.init_ui()
        self.setup_scheduler_connections()

    def setup_scheduler_connections(self):
        """设置定时检查相关的信号连接"""
        if hasattr(self.scheduler_manager, 'task_started'):
            self.scheduler_manager.task_started.connect(self.update_status)
        if hasattr(self.scheduler_manager, 'task_completed'):
            self.scheduler_manager.task_completed.connect(self.update_status)
        if hasattr(self.scheduler_manager, 'check_error'):
            self.scheduler_manager.check_error.connect(self.handle_scheduler_error)
        if hasattr(self.scheduler_manager, 'company_changed'):
            self.scheduler_manager.company_changed.connect(self.handle_company_changes)

    def handle_scheduler_error(self, company: str, error: str):
        """处理定时检查错误"""
        self.status_label.setText(f"检查 {company} 时出错: {error}")

    def handle_company_changes(self, company: str, changes: list):
        """处理企业变更"""
        if not changes:
            return

        message = f"企业 {company} 发生以下变更：\n\n"
        for change in changes:
            message += f"- {change.field_name}:\n"
            message += f"  原值: {change.old_value}\n"
            message += f"  新值: {change.new_value}\n"
            message += f"  变更时间: {change.change_time}\n\n"

        QMessageBox.information(self, '企业信息变更提醒', message)

    def init_ui(self):
        layout = QVBoxLayout()

        # 查询区域
        query_layout = QVBoxLayout()

        # 单个查询
        single_query_layout = QHBoxLayout()
        self.query_input = QLineEdit()
        self.query_input.setPlaceholderText('请输入公司名称、统一社会信用代码或注册号')
        self.query_input.returnPressed.connect(self.single_query)
        single_query_btn = QPushButton('单一查询')
        single_query_btn.clicked.connect(self.single_query)
        single_query_layout.addWidget(self.query_input)
        single_query_layout.addWidget(single_query_btn)
        query_layout.addLayout(single_query_layout)

        # 批量查询
        batch_query_layout = QHBoxLayout()
        self.batch_query_input = QTextEdit()
        self.batch_query_input.setPlaceholderText('请输入要批量查询的公司（每行一个）')
        self.batch_query_input.setMaximumHeight(100)
        batch_query_layout.addWidget(self.batch_query_input)
        query_layout.addLayout(batch_query_layout)

        # 批量查询按钮和进度条
        batch_control_layout = QHBoxLayout()
        batch_query_btn = QPushButton('批量查询')
        batch_query_btn.clicked.connect(self.batch_query)
        self.stop_batch_btn = QPushButton('停止查询')
        self.stop_batch_btn.clicked.connect(self.stop_batch_query)
        self.stop_batch_btn.setEnabled(False)

        # 进度条和状态标签
        progress_layout = QVBoxLayout()
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_label = QLabel()
        self.status_label.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.status_label)

        batch_control_layout.addWidget(batch_query_btn)
        batch_control_layout.addWidget(self.stop_batch_btn)
        query_layout.addLayout(batch_control_layout)
        query_layout.addLayout(progress_layout)

        # 添加定时检查按钮
        schedule_btn = QPushButton('定时检查设置')
        schedule_btn.clicked.connect(self.show_schedule_dialog)
        query_layout.addWidget(schedule_btn)

        layout.addLayout(query_layout)

        # 结果显示区域
        result_layout = QVBoxLayout()

        # 结果选择和操作按钮
        select_layout = QHBoxLayout()
        self.result_selector = QComboBox()
        self.result_selector.currentIndexChanged.connect(self.show_selected_result)
        self.result_selector.setVisible(False)

        clear_cache_btn = QPushButton('清除缓存')
        clear_cache_btn.clicked.connect(self.clear_cache)

        select_layout.addWidget(self.result_selector)
        select_layout.addWidget(clear_cache_btn)
        result_layout.addLayout(select_layout)

        # 缓存数据提醒条
        self.cache_reminder = QLabel("⚠️ 非最新数据 - 当前显示的是缓存数据")
        self.cache_reminder.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 4px;
                padding: 8px 12px;
                color: #856404;
                font-weight: bold;
                margin: 2px 0px;
            }
        """)
        self.cache_reminder.setVisible(False)
        result_layout.addWidget(self.cache_reminder)

        # 结果显示和导出
        self.result_display = QTextEdit()
        self.result_display.setReadOnly(True)

        # 导出和调试按钮布局
        export_layout = QHBoxLayout()
        export_btn = QPushButton('导出到Excel')
        export_btn.clicked.connect(self.export_to_excel)

        # 添加隐蔽的调试按钮（小尺寸，放在右侧）
        debug_btn = QPushButton('🐛')
        debug_btn.setMaximumWidth(30)
        debug_btn.setMaximumHeight(25)
        debug_btn.setToolTip('显示原始JSON数据（调试用）')
        debug_btn.clicked.connect(self.show_debug_json)

        export_layout.addWidget(export_btn)
        export_layout.addStretch()  # 添加弹性空间
        export_layout.addWidget(debug_btn)

        result_layout.addWidget(self.result_display)
        result_layout.addLayout(export_layout)

        layout.addLayout(result_layout)
        self.setLayout(layout)

    def update_cache_reminder(self, is_cached=False):
        """更新缓存数据提醒的显示状态"""
        self.is_current_data_cached = is_cached
        self.cache_reminder.setVisible(is_cached)

    def show_schedule_dialog(self):
        """显示定时检查设置对话框"""
        dialog = ScheduleDialog(self.scheduler_manager, self)
        dialog.exec_()  # 不需要单独处理结果，因为 ScheduleDialog 会自动保存更改

    def _cache_result(self, query_string: str, data: dict):
        """缓存查询结果 - 使用用户输入的查询字符串作为缓存键"""
        try:
            logging.info(f"开始缓存查询结果，查询字符串: '{query_string}'")

            if not data or not isinstance(data, dict):
                logging.warning(f"缓存失败: 数据为空或格式无效，查询: '{query_string}'")
                return

            if not query_string or not query_string.strip():
                logging.warning("缓存失败: 查询字符串为空")
                return

            # 使用查询字符串作为缓存键（去除首尾空格但保持原始大小写）
            cache_key = query_string.strip()
            logging.info(f"使用缓存键: '{cache_key}'")

            # 保存到持久化存储
            with shelve.open(self.cache_file) as cache:
                cache[cache_key] = {
                    'data': data,
                    'query_string': cache_key,
                    'timestamp': datetime.now().isoformat()
                }
                logging.info(f"已保存到持久化缓存: '{cache_key}'")

            # 同时更新内存缓存
            self.query_results[cache_key] = data
            logging.info(f"已添加到内存缓存: '{cache_key}'")

            # 尝试从API响应中提取企业信息用于日志
            company_info = "未知企业"
            try:
                if 'result' in data and isinstance(data['result'], dict):
                    result_data = data['result']
                    company_name = result_data.get('entName', '')
                    credit_code = result_data.get('creditCode', '')
                    if company_name:
                        company_info = company_name
                    elif credit_code:
                        company_info = f"企业(信用代码: {credit_code})"
            except Exception:
                pass

            logging.info(f"✅ 缓存完成: 查询'{cache_key}' -> {company_info}")

            # 验证缓存是否成功
            with shelve.open(self.cache_file) as cache:
                if cache_key in cache:
                    logging.info(f"✅ 验证成功: '{cache_key}' 已存在于持久化缓存中")
                else:
                    logging.error(f"❌ 验证失败: '{cache_key}' 未找到于持久化缓存中")

        except Exception as e:
            logging.error(f"缓存数据时出错: {str(e)}", exc_info=True)

    def load_cache(self):
        """加载缓存数据 - 支持新的用户输入缓存策略和向后兼容"""
        # 初始化空缓存
        self.query_results = {}

        try:
            if not os.path.exists(self.cache_file + ".dat"):
                logging.info("缓存文件不存在，使用空缓存")
                return

            logging.info(f"开始加载缓存文件: {self.cache_file}.dat")

            with shelve.open(self.cache_file) as cache:
                logging.info(f"缓存文件包含 {len(cache)} 个条目")

                # 处理三种可能的缓存格式
                if 'results' in cache:
                    # 最旧格式：直接存储的结果
                    try:
                        self.query_results = dict(cache['results'])
                        logging.info("已加载最旧格式缓存")
                    except Exception as e:
                        logging.warning(f"加载最旧格式缓存失败: {str(e)}")
                else:
                    # 处理新格式和旧格式的混合缓存
                    loaded_count = 0
                    error_count = 0
                    new_format_count = 0
                    old_format_count = 0

                    for k, v in cache.items():
                        try:
                            logging.info(f"处理缓存条目: '{k}'")

                            if isinstance(v, dict) and 'data' in v:
                                # 检查是否是新格式（用户输入缓存）
                                if 'query_string' in v:
                                    # 新格式：用户输入作为缓存键
                                    query_string = v['query_string']
                                    data = v['data']

                                    # 直接使用查询字符串作为缓存键
                                    self.query_results[query_string] = data
                                    logging.info(f"加载新格式缓存: '{query_string}'")
                                    new_format_count += 1
                                    loaded_count += 1

                                elif isinstance(v['data'], dict) and 'result' in v['data']:
                                    # 旧格式：企业名称作为缓存键，需要生成多个查询键
                                    full_data = v['data']
                                    result_data = full_data['result']

                                    # 为向后兼容，仍然使用企业名称作为一个缓存键
                                    self.query_results[k] = full_data
                                    logging.info(f"加载旧格式缓存(主键): '{k}'")

                                    # 创建多个缓存键以支持不同的查询方式
                                    try:
                                        cache_keys = self._generate_cache_keys(result_data)
                                        logging.info(f"为旧格式企业 '{k}' 生成额外缓存键: {cache_keys}")

                                        # 为每个可能的查询关键词创建缓存条目
                                        for cache_key in cache_keys:
                                            if cache_key and cache_key != k:  # 避免重复添加主键
                                                self.query_results[cache_key] = full_data
                                                logging.info(f"添加旧格式额外缓存键: '{cache_key}'")
                                    except Exception as e:
                                        logging.warning(f"为旧格式企业 '{k}' 生成额外缓存键时出错: {str(e)}")

                                    old_format_count += 1
                                    loaded_count += 1
                                else:
                                    logging.warning(f"缓存条目 '{k}' 数据格式不正确")
                            else:
                                logging.warning(f"缓存条目 '{k}' 格式不正确: {type(v)}")

                        except Exception as e:
                            error_count += 1
                            logging.warning(f"加载缓存条目 '{k}' 时出错: {str(e)}")
                            continue  # 跳过有问题的条目，继续加载其他条目

                    logging.info(f"缓存加载统计: 总计 {loaded_count} 个条目 (新格式: {new_format_count}, 旧格式: {old_format_count}, 错误: {error_count})")

            logging.info(f"缓存加载完成，共 {len(self.query_results)} 个查询条目")
            if self.query_results:
                # 只显示前10个键作为示例，避免日志过长
                sample_keys = list(self.query_results.keys())[:10]
                logging.info(f"可用的缓存键示例: {sample_keys}")
                if len(self.query_results) > 10:
                    logging.info(f"... 还有 {len(self.query_results) - 10} 个缓存键")

        except Exception as e:
            logging.error(f"加载缓存时发生严重错误: {str(e)}", exc_info=True)
            # 发生严重错误时，保持空缓存但不影响程序运行
            self.query_results = {}

    def _generate_cache_keys(self, result_data: dict) -> list:
        """为企业数据生成多个缓存键，支持不同的查询方式"""
        cache_keys = []

        # 企业名称
        ent_name = result_data.get('entName', '').strip()
        if ent_name:
            cache_keys.append(ent_name)

        # 统一社会信用代码
        credit_code = result_data.get('creditCode', '').strip()
        if credit_code:
            cache_keys.append(credit_code)

        # 注册号
        reg_number = result_data.get('regNumber', '').strip()
        if reg_number:
            cache_keys.append(reg_number)

        # 企业ID（如果存在）
        ent_id = result_data.get('id', '').strip()
        if ent_id:
            cache_keys.append(str(ent_id))

        # 英文名称（如果存在）
        property3 = result_data.get('property3', '').strip()
        if property3:
            cache_keys.append(property3)

        # 移除重复项并返回
        return list(set(cache_keys))

    def clear_cache(self):
        """清除缓存"""
        try:
            with shelve.open(self.cache_file) as cache:
                cache.clear()
            self.query_results = {}
            QMessageBox.information(self, "成功", "缓存已清除")
        except Exception as e:
            logging.error(f"清除缓存时出错: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"清除缓存失败: {str(e)}")

    def save_cache(self):
        """保存缓存数据"""
        try:
            with shelve.open(self.cache_file) as cache:
                for key, data in self.query_results.items():
                    cache[key] = {
                        'data': {'result': data},
                        'timestamp': datetime.now().isoformat()
                    }
        except Exception as e:
            logging.error(f"保存缓存时出错: {str(e)}", exc_info=True)

    def single_query(self):
        """执行单一查询 - 使用用户输入缓存策略"""
        keyword = self.query_input.text().strip()
        if not keyword:
            QMessageBox.warning(self, '警告', '请输入查询关键词！')
            return

        logging.info(f"开始查询: '{keyword}'")
        logging.info(f"当前缓存中有 {len(self.query_results)} 个条目")

        # 使用精确匹配检查缓存（用户输入的查询字符串）
        if keyword in self.query_results:
            logging.info(f"✅ 在缓存中找到精确匹配: '{keyword}'")

            # 尝试从缓存数据中提取企业信息用于显示
            cached_data = self.query_results[keyword]
            company_info = "缓存的企业"
            try:
                if isinstance(cached_data, dict) and 'result' in cached_data:
                    result_data = cached_data['result']
                    if isinstance(result_data, dict):
                        company_name = result_data.get('entName', '')
                        if company_name:
                            company_info = company_name
                        else:
                            credit_code = result_data.get('creditCode', '')
                            if credit_code:
                                company_info = f"企业信用代码：{credit_code}"
            except Exception:
                pass

            reply = QMessageBox.question(
                self, '提示',
                f'查询"{keyword}"的信息已在缓存中({company_info})，是否重新查询？',
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.No:
                logging.info("用户选择使用缓存数据")
                self.show_cached_result(keyword)
                return
            else:
                logging.info("用户选择重新查询")
        else:
            logging.info(f"缓存中未找到精确匹配: '{keyword}'")

            # 检查是否有相似的键（调试用）
            similar_keys = [k for k in self.query_results.keys() if keyword.lower() in k.lower() or k.lower() in keyword.lower()]
            if similar_keys:
                logging.info(f"找到相似的缓存键: {similar_keys[:5]}")  # 只显示前5个
            else:
                logging.info("未找到相似的缓存键")

        self.disable_controls(True)
        self.status_label.setText(f"正在查询: {keyword}")
        self.status_label.setVisible(True)

        # 执行查询
        try:
            logging.info(f"发起API查询: '{keyword}'")
            result = self.api_client.query_company_info(keyword)
            if result['success']:
                data = result['data']
                if data:
                    # 更新内存缓存（用于当前会话的结果选择器）
                    self.query_results[keyword] = data
                    self.current_result = data

                    # 缓存到持久化存储（使用新的用户输入缓存策略）
                    self._cache_result(keyword, data)

                    self.update_result_display(data)
                    self.update_result_selector([keyword])
                    self.status_label.setText("查询成功")
                    self.update_cache_reminder(is_cached=False)  # 隐藏缓存提醒（实时数据）
                    logging.info(f"✅ 查询成功: '{keyword}'")
                else:
                    self.status_label.setText("未找到数据")
                    QMessageBox.warning(self, '查询结果', '未找到相关企业信息')
                    logging.warning(f"API返回空数据: '{keyword}'")
            else:
                self.status_label.setText(f"查询失败: {result['message']}")
                QMessageBox.warning(self, '查询失败', result['message'])
                logging.warning(f"API查询失败: '{keyword}' - {result['message']}")

        except Exception as e:
            error_msg = f"查询过程中出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            self.status_label.setText("查询出错")
            QMessageBox.critical(self, '错误', error_msg)

        finally:
            self.disable_controls(False)

    def show_cached_result(self, keyword):
        data = self.query_results.get(keyword)
        if data:
            self.current_result = data
            self.update_result_display(data)
            self.update_result_selector([keyword])
            self.status_label.setText("显示缓存数据")
            self.status_label.setVisible(True)
            self.update_cache_reminder(is_cached=True)  # 显示缓存提醒

    def batch_query(self):
        """批量查询"""
        # 获取关键词列表
        keywords = [kw.strip() for kw in self.batch_query_input.toPlainText().split('\n') if kw.strip()]

        if not keywords:
            QMessageBox.warning(self, '警告', '请输入要查询的企业名称！')
            return

        # 如果只有一个关键词，直接使用单一查询功能
        if len(keywords) == 1:
            self.query_input.setText(keywords[0])
            self.single_query()
            return

        # 保存当前缓存状态，以便在批量查询时恢复
        cached_results = self.query_results.copy()

        self.query_worker = QueryWorker(self.api_client, keywords)
        self.query_worker.progress_updated.connect(self.update_progress)
        self.query_worker.query_completed.connect(self.handle_query_result)
        self.query_worker.query_error.connect(self.handle_query_error)
        self.query_worker.status_updated.connect(lambda msg: self.status_label.setText(msg))

        # 只清空显示结果，保留缓存数据
        self.result_display.clear()

        # 为批量查询创建临时结果存储，不影响主缓存
        self.batch_query_results = {}

        # 开始查询
        self.stop_batch_btn.setEnabled(True)
        self.query_worker.start()

    def show_batch_cached_results(self, keywords):
        for keyword in keywords:
            if keyword in self.query_results:
                self.update_result_selector(list(self.query_results.keys()))
        if len(self.query_results) > 0:
            self.show_selected_result(0)
        self.status_label.setText("显示缓存数据")
        self.status_label.setVisible(True)

    def update_status(self, message: str):
        """更新状态信息"""
        if hasattr(self, 'status_label'):
            self.status_label.setText(message)

    def stop_batch_query(self):
        if self.query_worker:
            self.query_worker.stop()
            self.status_label.setText("正在停止查询...")

    def update_progress(self, current, total):
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)

    def handle_query_result(self, result):
        keyword = result['keyword']
        data = result['data']

        # 添加到主缓存（使用新的用户输入缓存策略）
        self._cache_result(keyword, data)

        # 为批量查询结果显示添加到临时存储
        if hasattr(self, 'batch_query_results'):
            self.batch_query_results[keyword] = data
            # 更新结果选择器显示批量查询结果
            self.update_result_selector(list(self.batch_query_results.keys()))
            if len(self.batch_query_results) == 1:
                self.current_result = data
                self.update_result_display(data)
        else:
            # 单一查询的情况
            self.query_results[keyword] = data
            self.current_result = data
            self.update_result_display(data)
            self.update_result_selector([keyword])

    def handle_query_error(self, error_msg):
        self.result_display.append(f"错误: {error_msg}")
        self.status_label.setText(error_msg)

    def query_finished(self):
        self.disable_controls(False)
        QMessageBox.information(self, '完成', '批量查询已完成！')
        self.status_label.setText("查询完成")

    def disable_controls(self, disabled):
        self.query_input.setEnabled(not disabled)
        self.batch_query_input.setEnabled(not disabled)
        self.progress_bar.setVisible(disabled)
        self.stop_batch_btn.setEnabled(disabled)

    def update_result_selector(self, keywords):
        self.result_selector.clear()
        if len(keywords) > 1:
            self.result_selector.setVisible(True)
            self.result_selector.addItems(keywords)
        else:
            self.result_selector.setVisible(False)

    def show_selected_result(self, index):
        if index >= 0:
            keyword = self.result_selector.itemText(index)
            data = self.query_results.get(keyword)
            if data:
                self.current_result = data
                self.update_result_display(data)
                # 检查是否为批量查询结果（实时数据）还是缓存数据
                is_cached = not hasattr(self, 'batch_query_results') or keyword not in getattr(self, 'batch_query_results', {})
                self.update_cache_reminder(is_cached=is_cached)

    def update_result_display(self, data):
        """在GUI中显示完整的企业信息"""
        try:
            if not data or not isinstance(data, dict):
                self.result_display.setText("数据为空或格式无效")
                return

            # 使用数据处理器解析数据
            if not self.data_processor.parse_company_info(data):
                self.result_display.setText("数据解析失败")
                return

            # 获取格式化后的显示文本
            formatted_text = self._format_data_for_display(data)
            self.result_display.setText(formatted_text)

        except Exception as e:
            error_msg = f"显示数据时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            self.result_display.setText(error_msg)

    def _format_data_for_display(self, data: dict) -> str:
        """格式化数据用于显示 - 根据配置只显示勾选的部分"""
        try:
            if not data or 'result' not in data:
                return "数据为空或格式无效"

            result_data = data['result']
            display_config = self.config_manager.display_config
            result = []

            # 基本信息始终显示
            basic_section = display_config.sections.get('basic_info', {})
            if basic_section:
                result.append(f"\n【{basic_section.get('title', '基本信息')}】")
                for field in basic_section.get('fields', []):
                    key = field['key']  # 这是display config中的key (如 unifiedCode, legalPerson)
                    display = field['display']

                    # 需要将display config key转换为JSON field name
                    json_field_name = self._get_json_field_name(key)
                    value = result_data.get(json_field_name, '')

                    if value:
                        # 清理HTML并格式化
                        cleaned_value = self.data_processor._clean_html(str(value))
                        formatted_value = self._format_long_text(cleaned_value, display)
                        result.append(f"{display}: {formatted_value}")
                    elif key in ['unifiedCode', 'legalPerson']:  # 对于目标字段，即使为空也显示
                        result.append(f"{display}: 暂无")

            # 处理其他部分 - 只显示启用的部分
            for section_name, section in display_config.sections.items():
                if section_name == 'basic_info':  # 基本信息已处理
                    continue

                if not section.get('enabled', False):  # 只显示启用的部分
                    continue

                section_title = section.get('title', section_name)
                section_content = []

                # 处理列表数据
                if 'list_key' in section:
                    list_key = section['list_key']
                    data_list = result_data.get(list_key, [])

                    if not data_list:
                        section_content.append("暂无数据")
                    else:
                        for item in data_list:
                            item_result = []
                            for field in section['fields']:
                                key = field['key']
                                display = field['display']
                                value = item.get(key, '')
                                if value:
                                    if isinstance(value, list):
                                        value = '、'.join(map(str, value))
                                    # 清理HTML并格式化
                                    cleaned_value = self.data_processor._clean_html(str(value))
                                    formatted_value = self._format_long_text(cleaned_value, display)
                                    item_result.append(f"{display}: {formatted_value}")
                            if item_result:
                                section_content.append('  ' + '\n  '.join(item_result))
                                section_content.append('-' * 50)

                # 处理对象数据（如简易注销信息）
                elif 'objectKey' in section:
                    obj_key = section['objectKey']
                    obj_data = result_data.get(obj_key, None)

                    if not obj_data:
                        section_content.append("暂无数据")
                    else:
                        # 显示主要字段
                        for field in section.get('fields', []):
                            key = field['key']
                            display = field['display']
                            value = obj_data.get(key, '')
                            if value:
                                cleaned_value = self.data_processor._clean_html(str(value))
                                formatted_value = self._format_long_text(cleaned_value, display)
                                section_content.append(f"{display}: {formatted_value}")

                        # 处理子部分
                        if 'subSections' in section:
                            for sub_name, sub_section in section['subSections'].items():
                                if sub_name in obj_data:
                                    section_content.append(f"\n  {sub_section.get('title', sub_name)}:")
                                    sub_list = obj_data[sub_name]
                                    if not sub_list:
                                        section_content.append("  暂无数据")
                                    else:
                                        for item in sub_list:
                                            item_result = []
                                            for field in sub_section['fields']:
                                                key = field['key']
                                                display = field['display']
                                                value = item.get(key, '')
                                                if value:
                                                    cleaned_value = self.data_processor._clean_html(str(value))
                                                    formatted_value = self._format_long_text(cleaned_value, display)
                                                    item_result.append(f"{display}: {formatted_value}")
                                            if item_result:
                                                section_content.append('    ' + '  |  '.join(item_result))
                                                section_content.append('    ' + '-' * 45)

                # 如果该部分有内容，添加到结果中
                if section_content:
                    result.append(f"\n【{section_title}】")
                    result.extend(section_content)

            return '\n'.join(result) if result else "未找到数据"

        except Exception as e:
            error_msg = f"格式化数据时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            return error_msg

    def _format_long_text(self, text: str, field_name: str) -> str:
        """格式化长文本，改善阅读体验"""
        if not isinstance(text, str):
            return str(text)

        # 对于特定字段进行特殊处理
        long_text_fields = ['经营范围', '变更前', '变更后', '变更事项', '处罚内容', '违法事实', '列入原因', '移出原因']

        # 如果文本长度超过100字符或包含换行符，进行格式化
        if len(text) > 100 or '\n' in text or any(field in field_name for field in long_text_fields):
            # 按句号、分号等分割长文本
            import re
            # 分割标点符号
            sentences = re.split(r'[。；;]', text)
            if len(sentences) > 1:
                # 每个句子一行，并添加适当缩进
                formatted_sentences = []
                for sentence in sentences:
                    sentence = sentence.strip()
                    if sentence:
                        formatted_sentences.append(f"    {sentence}")
                return '\n' + '\n'.join(formatted_sentences)
            else:
                # 如果没有明显的句子分割，按长度分行
                if len(text) > 100:
                    lines = []
                    words = text.split()
                    current_line = ""
                    for word in words:
                        if len(current_line + word) > 80:
                            if current_line:
                                lines.append(f"    {current_line.strip()}")
                                current_line = word + " "
                            else:
                                lines.append(f"    {word}")
                                current_line = ""
                        else:
                            current_line += word + " "
                    if current_line.strip():
                        lines.append(f"    {current_line.strip()}")
                    return '\n' + '\n'.join(lines)

        return text

    def _get_json_field_name(self, display_config_key: str) -> str:
        """将display config中的key转换为JSON中的field name

        Args:
            display_config_key: display_config.json中的key (如 unifiedCode, legalPerson)

        Returns:
            JSON中对应的field name (如 creditCode, legalPersonName)
        """
        # 使用data_processor中的字段映射，但需要反向查找
        field_map = self.data_processor._basic_field_map

        # 反向查找：从 class_field -> api_field
        for json_field, class_field in field_map.items():
            if class_field == display_config_key:
                return json_field

        # 如果没找到映射，直接返回原key（向后兼容）
        return display_config_key

    def export_to_excel(self):
        """导出数据到Excel - 改进版：支持批量导出"""
        try:
            # 检查是否有批量查询结果
            has_batch_results = hasattr(self, 'batch_query_results') and self.batch_query_results
            has_multiple_results = (self.result_selector.isVisible() and
                                  self.result_selector.count() > 1)

            if has_batch_results or has_multiple_results:
                # 批量导出：导出所有结果
                self._export_batch_results()
            elif self.current_result:
                # 单一导出：导出当前结果
                self._export_single_result()
            else:
                QMessageBox.warning(self, "警告", "没有可导出的数据")

        except Exception as e:
            error_msg = f"导出Excel时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            QMessageBox.critical(self, "错误", error_msg)

    def _export_single_result(self):
        """导出单一查询结果"""
        # 获取保存路径
        file_name = f"企业信息_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        save_path, _ = QFileDialog.getSaveFileName(
            self, "保存Excel文件", file_name, "Excel 文件 (*.xlsx)"
        )

        if not save_path:
            return

        # 导出数据
        if self.data_processor.export_to_excel(self.current_result, save_path):
            QMessageBox.information(self, "成功", "企业信息导出成功！")
            # 打开文件所在文件夹
            os.startfile(os.path.dirname(save_path))
        else:
            QMessageBox.warning(self, "错误", "导出失败，请查看日志了解详细信息")

    def _export_batch_results(self):
        """导出批量查询结果"""
        # 获取要导出的数据
        export_data = {}

        # 优先使用批量查询结果
        if hasattr(self, 'batch_query_results') and self.batch_query_results:
            export_data.update(self.batch_query_results)
            data_source = "当前批量查询"
        else:
            # 如果没有批量查询结果，使用结果选择器中的所有项目
            if self.result_selector.isVisible():
                for i in range(self.result_selector.count()):
                    keyword = self.result_selector.itemText(i)
                    if keyword in self.query_results:
                        export_data[keyword] = self.query_results[keyword]
                data_source = "当前显示的查询结果"

        if not export_data:
            QMessageBox.warning(self, "警告", "没有可导出的批量数据")
            return

        # 获取保存路径
        file_name = f"企业信息批量导出_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        save_path, _ = QFileDialog.getSaveFileName(
            self, "保存Excel文件", file_name, "Excel 文件 (*.xlsx)"
        )

        if not save_path:
            return

        # 执行批量导出
        if self._export_multiple_companies_to_excel(export_data, save_path):
            QMessageBox.information(
                self, "成功",
                f"批量导出成功！\n\n"
                f"数据来源：{data_source}\n"
                f"导出企业数量：{len(export_data)} 个\n"
                f"文件已保存到：{save_path}"
            )
            # 打开文件所在文件夹
            os.startfile(os.path.dirname(save_path))
        else:
            QMessageBox.warning(self, "错误", "批量导出失败，请查看日志了解详细信息")

    def _generate_export_columns(self, display_config) -> list:
        """根据显示配置生成导出列定义"""
        columns = ['查询关键词']

        # 基本信息列
        basic_section = display_config.sections.get('basic_info', {})
        if basic_section.get('enabled', True):
            for field in basic_section.get('fields', []):
                columns.append(field['display'])

        # 其他启用的部分
        for section_name, section_config in display_config.sections.items():
            if section_name == 'basic_info':
                continue

            if not section_config.get('enabled', False):
                continue

            list_key = section_config.get('list_key')
            if list_key:
                # 添加数量列
                count_column = f"{section_config.get('title', section_name)}数量"
                columns.append(count_column)

                # 添加详细信息列（最多3条记录）
                for field in section_config.get('fields', []):
                    display_name = field['display']
                    # 为多条记录添加序号
                    for i in range(3):
                        column_name = f"{display_name}_{i+1}"
                        columns.append(column_name)

        return columns

    def _export_multiple_companies_to_excel(self, companies_data: dict, filename: str) -> bool:
        """导出多个企业数据到Excel文件，每个企业一行 - 改进版：支持显示配置"""
        try:
            logging.info(f"开始批量导出 {len(companies_data)} 个企业到Excel，使用显示配置")

            # 获取显示配置
            display_config = self.config_manager.display_config

            # 动态生成列定义
            column_definitions = self._generate_export_columns(display_config)
            logging.info(f"生成了 {len(column_definitions)} 个导出列")

            # 准备Excel数据
            excel_rows = []

            for keyword, company_data in companies_data.items():
                try:
                    # 解析企业数据
                    temp_processor = DataProcessor(self.config_manager.display_config)
                    if not temp_processor.parse_company_info(company_data):
                        logging.warning(f"解析企业数据失败: {keyword}")
                        continue

                    # 根据列定义提取数据
                    row_data = {'查询关键词': keyword}

                    # 处理基本信息
                    basic_section = display_config.sections.get('basic_info', {})
                    if basic_section.get('enabled', True):
                        for field in basic_section.get('fields', []):
                            field_key = field['key']
                            display_name = field['display']

                            # 直接从处理器获取属性值
                            value = temp_processor._process_value(getattr(temp_processor, field_key, ''))
                            row_data[display_name] = value

                    # 处理列表数据部分（如经营异常、变更记录等）
                    for section_name, section_config in display_config.sections.items():
                        if section_name == 'basic_info':
                            continue

                        if not section_config.get('enabled', False):
                            continue

                        list_key = section_config.get('list_key')
                        if list_key:
                            # 处理列表数据
                            items = company_data.get('result', {}).get(list_key, [])
                            if items:
                                # 统计数量
                                count_column = f"{section_config.get('title', section_name)}数量"
                                row_data[count_column] = len(items)

                                # 提取前几条记录的详细信息
                                for i, item in enumerate(items[:3]):  # 最多导出前3条
                                    for field in section_config.get('fields', []):
                                        field_key = field['key']
                                        display_name = field['display']
                                        column_name = f"{display_name}_{i+1}" if len(items) > 1 else display_name

                                        value = temp_processor._process_value(item.get(field_key, ''))
                                        row_data[column_name] = value
                            else:
                                # 无数据时填充空值
                                count_column = f"{section_config.get('title', section_name)}数量"
                                row_data[count_column] = 0

                    excel_rows.append(row_data)
                    logging.info(f"成功处理企业: {keyword} -> {row_data.get('企业名称', '未知')}")

                except Exception as e:
                    logging.error(f"处理企业 {keyword} 时出错: {str(e)}")
                    # 添加错误行
                    error_row = {'查询关键词': keyword, '企业名称': f'数据解析失败: {str(e)}'}
                    # 填充其他列为空值
                    for col_def in column_definitions:
                        if col_def not in error_row:
                            error_row[col_def] = ''
                    excel_rows.append(error_row)
                    continue

            if not excel_rows:
                logging.error("没有成功处理的企业数据")
                return False

            # 创建DataFrame并导出
            df = pd.DataFrame(excel_rows)

            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='企业信息汇总', index=False)

                # 格式化工作表
                worksheet = writer.sheets['企业信息汇总']

                # 调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)  # 最大宽度50
                    worksheet.column_dimensions[column_letter].width = adjusted_width

                # 冻结首行
                worksheet.freeze_panes = 'A2'

            logging.info(f"批量导出完成: {filename}, 共 {len(excel_rows)} 行数据")
            return True

        except Exception as e:
            error_msg = f"批量导出Excel时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            return False

    def show_debug_json(self):
        """显示原始JSON数据（调试用）"""
        try:
            if not self.current_result:
                QMessageBox.warning(self, "调试", "没有可显示的数据")
                return

            # 创建调试窗口
            debug_dialog = QDialog(self)
            debug_dialog.setWindowTitle('原始JSON数据 - 调试')
            debug_dialog.setGeometry(200, 200, 800, 600)

            layout = QVBoxLayout()

            # 添加说明标签
            info_label = QLabel("以下是未经任何处理的原始JSON数据：")
            layout.addWidget(info_label)

            # 添加JSON显示区域
            json_display = QTextEdit()
            json_display.setReadOnly(True)
            json_display.setFont(QFont("Consolas", 10))  # 使用等宽字体

            # 格式化JSON数据
            import json
            formatted_json = json.dumps(self.current_result, ensure_ascii=False, indent=2)
            json_display.setText(formatted_json)

            layout.addWidget(json_display)

            # 添加复制按钮
            copy_btn = QPushButton('复制到剪贴板')
            copy_btn.clicked.connect(lambda: self.copy_to_clipboard(formatted_json))
            layout.addWidget(copy_btn)

            # 添加关闭按钮
            close_btn = QPushButton('关闭')
            close_btn.clicked.connect(debug_dialog.close)
            layout.addWidget(close_btn)

            debug_dialog.setLayout(layout)
            debug_dialog.exec_()

        except Exception as e:
            error_msg = f"显示调试信息时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            QMessageBox.critical(self, "错误", error_msg)

    def copy_to_clipboard(self, text):
        """复制文本到剪贴板"""
        try:
            from PyQt5.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(text)
            QMessageBox.information(self, "成功", "JSON数据已复制到剪贴板")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"复制失败: {str(e)}")


class DishonestQueryWorker(QThread):
    """失信人查询工作线程 - 增强版：支持重试和更好的错误处理"""
    progress_updated = pyqtSignal(int, int)  # 当前进度，总数
    query_completed = pyqtSignal(dict)  # 查询结果
    query_error = pyqtSignal(str)  # 错误信息
    status_updated = pyqtSignal(str)  # 状态更新
    cache_prompt_needed = pyqtSignal(str, dict)  # 需要缓存提示的信号：关键词，缓存数据
    _process_next_signal = pyqtSignal()  # 内部信号用于触发下一个查询

    def __init__(self, api_client, keywords, query_results=None):
        super().__init__()
        self.api_client = api_client
        self.keywords = keywords
        self.query_results = query_results or {}
        self.is_running = True
        self.pending_queries = []  # 等待用户决定的查询队列
        self.current_keyword_index = 0
        self.max_retries = 2  # 最大重试次数
        self.retry_delay = 2000  # 重试延迟（毫秒）

        # 连接内部信号
        self._process_next_signal.connect(self._process_next_query)

    def run(self):
        total = len(self.keywords)
        logging.info(f"DishonestQueryWorker.run() started with {total} keywords: {self.keywords}")

        # 检查是否有关键词
        if not self.keywords:
            logging.warning("No keywords provided for batch query")
            self.status_updated.emit("没有要查询的关键词")
            self.finished.emit()
            return

        self.status_updated.emit(f"开始查询 {total} 个失信人信息...")

        # 首先检查所有关键词的缓存状态
        self.pending_queries = []
        for keyword in self.keywords:
            if keyword in self.query_results:
                # 发现缓存数据，需要用户决定
                self.pending_queries.append({
                    'keyword': keyword,
                    'has_cache': True,
                    'cache_data': self.query_results[keyword]
                })
                logging.info(f"Found cached data for keyword: {keyword}")
            else:
                # 没有缓存，直接查询
                self.pending_queries.append({
                    'keyword': keyword,
                    'has_cache': False,
                    'cache_data': None
                })
                logging.info(f"No cache for keyword, will query API: {keyword}")

        logging.info(f"Prepared {len(self.pending_queries)} queries for processing")

        # 开始处理队列
        self.current_keyword_index = 0
        # 直接调用_process_next_query()，因为信号连接已经在主线程中完成
        logging.info("Starting query processing...")
        self._process_next_query()

    def _process_next_query(self):
        """处理下一个查询 - 紧急修复版：确保正确处理查询队列"""
        try:
            # 详细调试信息
            logging.info(f"_process_next_query called: is_running={self.is_running}, current_index={self.current_keyword_index}, pending_count={len(self.pending_queries)}")

            if hasattr(self, 'pending_queries'):
                logging.info(f"Pending queries: {[q['keyword'] for q in self.pending_queries]}")
            else:
                logging.error("pending_queries attribute not found!")
                self.query_error.emit("内部错误：查询队列未初始化")
                return

            if not self.is_running:
                logging.info("Worker stopped, exiting _process_next_query")
                return

            # 检查是否所有查询都已处理完成
            if self.current_keyword_index >= len(self.pending_queries):
                logging.info("All queries processed, finishing")
                self.status_updated.emit("查询完成")
                self.finished.emit()
                return

            # 获取当前要处理的查询
            query_info = self.pending_queries[self.current_keyword_index]
            keyword = query_info['keyword']

            logging.info(f"Processing query {self.current_keyword_index + 1}/{len(self.pending_queries)}: {keyword}")

            # 更新进度
            self.progress_updated.emit(self.current_keyword_index + 1, len(self.pending_queries))

            if query_info['has_cache']:
                # 有缓存数据，发送信号请求用户决定
                logging.info(f"Found cache for {keyword}, prompting user")
                self.status_updated.emit(f"发现缓存数据: {keyword}")
                self.cache_prompt_needed.emit(keyword, query_info['cache_data'])
                # 注意：不要在这里调用_move_to_next_query()，等待用户决定
            else:
                # 没有缓存，直接执行API查询（带重试）
                logging.info(f"No cache for {keyword}, executing API query")
                self._execute_api_query_with_retry(keyword)

        except Exception as e:
            logging.error(f"Error in _process_next_query: {str(e)}", exc_info=True)
            self.query_error.emit(f"处理查询时出错: {str(e)}")
            self._move_to_next_query()  # 继续处理下一个查询

    def handle_cache_decision(self, keyword, use_cache):
        """处理用户的缓存决定"""
        if not self.is_running:
            return

        query_info = self.pending_queries[self.current_keyword_index]

        if use_cache:
            # 使用缓存数据
            self.status_updated.emit(f"使用缓存数据: {keyword}")
            self.query_completed.emit({
                'keyword': keyword,
                'data': query_info['cache_data'],
                'from_cache': True
            })
            self._move_to_next_query()
        else:
            # 执行新的API查询（带重试）
            self._execute_api_query_with_retry(keyword)

    def _execute_api_query_with_retry(self, keyword, retry_count=0):
        """执行API查询（带重试逻辑）"""
        self.status_updated.emit(f"正在查询: {keyword}" + (f" (重试 {retry_count}/{self.max_retries})" if retry_count > 0 else ""))

        try:
            result = self.api_client.query_dishonest_person(keyword)

            if result['success']:
                self.query_completed.emit({
                    'keyword': keyword,
                    'data': result['data'],
                    'from_cache': False
                })
                self._move_to_next_query()
            else:
                # 查询失败，检查是否需要重试
                if retry_count < self.max_retries and self._should_retry_error(result['message']):
                    # 延迟后重试
                    self.status_updated.emit(f"查询 {keyword} 失败，{self.retry_delay/1000}秒后重试...")
                    self.msleep(self.retry_delay)  # 线程安全的延迟
                    self._execute_api_query_with_retry(keyword, retry_count + 1)
                else:
                    # 达到最大重试次数或不应重试的错误，报告失败
                    error_msg = f"查询 {keyword} 失败: {result['message']}"
                    if retry_count > 0:
                        error_msg += f" (已重试 {retry_count} 次)"
                    self.query_error.emit(error_msg)
                    self._move_to_next_query()

        except Exception as e:
            # 异常情况，检查是否需要重试
            if retry_count < self.max_retries:
                # 延迟后重试
                self.status_updated.emit(f"查询 {keyword} 出错，{self.retry_delay/1000}秒后重试...")
                self.msleep(self.retry_delay)  # 线程安全的延迟
                self._execute_api_query_with_retry(keyword, retry_count + 1)
            else:
                # 达到最大重试次数，报告失败
                error_msg = f"查询 {keyword} 时出错: {str(e)}"
                if retry_count > 0:
                    error_msg += f" (已重试 {retry_count} 次)"
                self.query_error.emit(error_msg)
                self._move_to_next_query()

    def _should_retry_error(self, error_message):
        """判断是否应该重试的错误"""
        # 对于网络相关错误、超时错误、频率限制等可以重试
        retry_keywords = [
            '请求超时', '连接错误', '网络请求错误', '访问频率过快',
            '请求被服务器拦截', 'HTTP错误', '服务器返回空响应'
        ]
        return any(keyword in error_message for keyword in retry_keywords)

    def _execute_api_query(self, keyword):
        """执行API查询（保留原方法以兼容性）"""
        self._execute_api_query_with_retry(keyword, 0)

    def _move_to_next_query(self):
        """移动到下一个查询"""
        self.current_keyword_index += 1
        logging.info(f"Moving to next query: index now {self.current_keyword_index}/{len(self.pending_queries)}")

        # 添加延迟避免请求过快（仅在不是最后一个查询时）
        if self.current_keyword_index < len(self.pending_queries):
            self.status_updated.emit("等待下一次查询...")
            # 使用线程安全的方式延迟调用
            self.msleep(1000)  # 1秒延迟
            self._process_next_signal.emit()
        else:
            # 所有查询都已处理完成 - 立即触发完成逻辑
            logging.info("All queries completed, will finish on next _process_next_query call")
            self._process_next_signal.emit()

    def stop(self):
        self.is_running = False


class DishonestPersonTab(QWidget):
    """失信被执行人查询选项卡"""
    def __init__(self, api_client: APIClient):
        super().__init__()
        self.api_client = api_client
        self.data_processor = DishonestDataProcessor()
        self.scheduler_manager = DishonestSchedulerManager(self.api_client)
        self.query_results = {}
        self.historical_query_results = {}  # 存储历史查询结果
        self.current_result = None
        self.query_worker = None
        self.cache_file = "dishonest_cache"
        self.is_current_data_cached = False  # 跟踪当前显示的数据是否来自缓存

        # 新增：跟踪每个对象的缓存状态
        self.object_cache_status = {}  # 格式: {keyword: True/False} - True表示来自缓存，False表示来自API
        self.batch_query_errors = []  # 跟踪批量查询中的错误

        self.load_cache()
        self.init_ui()
        self.setup_scheduler_connections()

    def setup_scheduler_connections(self):
        """设置定时检查相关的信号连接"""
        # 失信人调度器的信号连接（如果需要的话）
        pass

    def init_ui(self):
        layout = QVBoxLayout()

        # 查询区域
        query_layout = QVBoxLayout()

        # 单个查询
        single_query_layout = QHBoxLayout()
        self.query_input = QLineEdit()
        self.query_input.setPlaceholderText('请输入公司名称、统一社会信用代码或注册号')
        self.query_input.returnPressed.connect(self.single_query)
        single_query_btn = QPushButton('单一查询')
        single_query_btn.clicked.connect(self.single_query)
        single_query_layout.addWidget(self.query_input)
        single_query_layout.addWidget(single_query_btn)
        query_layout.addLayout(single_query_layout)

        # 批量查询
        batch_query_layout = QHBoxLayout()
        self.batch_query_input = QTextEdit()
        self.batch_query_input.setPlaceholderText('请输入要批量查询的企业（每行一个）')
        self.batch_query_input.setMaximumHeight(100)
        batch_query_layout.addWidget(self.batch_query_input)
        query_layout.addLayout(batch_query_layout)

        # 批量查询按钮和进度条
        batch_control_layout = QHBoxLayout()
        batch_query_btn = QPushButton('批量查询')
        batch_query_btn.clicked.connect(self.batch_query)
        self.stop_batch_btn = QPushButton('停止查询')
        self.stop_batch_btn.clicked.connect(self.stop_batch_query)
        self.stop_batch_btn.setEnabled(False)

        # 进度条和状态标签
        progress_layout = QVBoxLayout()
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_label = QLabel()
        self.status_label.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.status_label)

        batch_control_layout.addWidget(batch_query_btn)
        batch_control_layout.addWidget(self.stop_batch_btn)
        query_layout.addLayout(batch_control_layout)
        query_layout.addLayout(progress_layout)

        # 添加定时检查按钮
        schedule_btn = QPushButton('定时检查设置')
        schedule_btn.clicked.connect(self.show_schedule_dialog)
        query_layout.addWidget(schedule_btn)

        layout.addLayout(query_layout)

        # 结果显示区域
        result_layout = QVBoxLayout()

        # 结果选择和操作按钮
        select_layout = QHBoxLayout()
        self.result_selector = QComboBox()
        self.result_selector.currentIndexChanged.connect(self.show_selected_result)
        self.result_selector.setVisible(False)

        clear_cache_btn = QPushButton('清除缓存')
        clear_cache_btn.clicked.connect(self.clear_cache)

        select_layout.addWidget(self.result_selector)
        select_layout.addWidget(clear_cache_btn)
        result_layout.addLayout(select_layout)

        # 缓存数据提醒条
        self.cache_reminder = QLabel("⚠️ 非最新数据 - 当前显示的是缓存数据")
        self.cache_reminder.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 4px;
                padding: 8px 12px;
                color: #856404;
                font-weight: bold;
                margin: 2px 0px;
            }
        """)
        self.cache_reminder.setVisible(False)
        result_layout.addWidget(self.cache_reminder)

        # 结果显示和导出
        self.result_display = QTextEdit()
        self.result_display.setReadOnly(True)

        # 导出和调试按钮布局
        export_layout = QHBoxLayout()
        export_btn = QPushButton('导出到Excel')
        export_btn.clicked.connect(self.export_to_excel)

        # 添加隐蔽的调试按钮（小尺寸，放在右侧）
        debug_btn = QPushButton('🐛')
        debug_btn.setMaximumWidth(30)
        debug_btn.setMaximumHeight(25)
        debug_btn.setToolTip('显示原始JSON数据（调试用）')
        debug_btn.clicked.connect(self.show_debug_json)

        export_layout.addWidget(export_btn)
        export_layout.addStretch()  # 添加弹性空间
        export_layout.addWidget(debug_btn)

        result_layout.addWidget(self.result_display)
        result_layout.addLayout(export_layout)

        layout.addLayout(result_layout)
        self.setLayout(layout)

    def update_cache_reminder(self, is_cached=False):
        """更新缓存数据提醒的显示状态"""
        self.is_current_data_cached = is_cached
        self.cache_reminder.setVisible(is_cached)

    def show_schedule_dialog(self):
        """显示定时检查设置对话框"""
        dialog = DishonestScheduleDialog(self.scheduler_manager, self)
        dialog.exec_()

    def load_cache(self):
        """加载缓存数据"""
        self.query_results = {}
        try:
            if not os.path.exists(self.cache_file + ".dat"):
                logging.info("失信人缓存文件不存在，使用空缓存")
                return

            logging.info(f"开始加载失信人缓存文件: {self.cache_file}.dat")

            with shelve.open(self.cache_file) as cache:
                logging.info(f"失信人缓存文件包含 {len(cache)} 个条目")

                for k, v in cache.items():
                    try:
                        if isinstance(v, dict) and 'data' in v:
                            self.query_results[k] = v['data']
                            logging.info(f"加载失信人缓存: '{k}'")
                    except Exception as e:
                        logging.warning(f"加载失信人缓存条目 '{k}' 时出错: {str(e)}")
                        continue

            logging.info(f"失信人缓存加载完成，共 {len(self.query_results)} 个查询条目")

        except Exception as e:
            logging.error(f"加载失信人缓存时发生错误: {str(e)}", exc_info=True)
            self.query_results = {}

    def _cache_result(self, query_string: str, data: dict):
        """缓存查询结果"""
        try:
            logging.info(f"开始缓存失信人查询结果，查询字符串: '{query_string}'")

            if not data or not isinstance(data, dict):
                logging.warning(f"失信人缓存失败: 数据为空或格式无效，查询: '{query_string}'")
                return

            if not query_string or not query_string.strip():
                logging.warning("失信人缓存失败: 查询字符串为空")
                return

            cache_key = query_string.strip()
            logging.info(f"使用失信人缓存键: '{cache_key}'")

            # 保存到持久化存储
            with shelve.open(self.cache_file) as cache:
                cache[cache_key] = {
                    'data': data,
                    'query_string': cache_key,
                    'timestamp': datetime.now().isoformat()
                }
                logging.info(f"已保存到失信人持久化缓存: '{cache_key}'")

            # 同时更新内存缓存
            self.query_results[cache_key] = data
            logging.info(f"已添加到失信人内存缓存: '{cache_key}'")

        except Exception as e:
            logging.error(f"缓存失信人数据时出错: {str(e)}", exc_info=True)

    def clear_cache(self):
        """清除缓存"""
        try:
            with shelve.open(self.cache_file) as cache:
                cache.clear()
            self.query_results = {}
            QMessageBox.information(self, "成功", "失信人缓存已清除")
        except Exception as e:
            logging.error(f"清除失信人缓存时出错: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"清除失信人缓存失败: {str(e)}")

    def single_query(self):
        """执行单一查询"""
        keyword = self.query_input.text().strip()
        if not keyword:
            QMessageBox.warning(self, '警告', '请输入查询关键词！')
            return

        logging.info(f"开始失信人查询: '{keyword}'")

        # 检查缓存
        if keyword in self.query_results:
            logging.info(f"✅ 在失信人缓存中找到: '{keyword}'")
            reply = QMessageBox.question(
                self, '提示',
                f'查询"{keyword}"的失信人信息已在缓存中，是否重新查询？',
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.No:
                logging.info("用户选择使用失信人缓存数据")
                self.show_cached_result(keyword)
                return

        self.disable_controls(True)
        self.status_label.setText(f"正在查询失信人: {keyword}")
        self.status_label.setVisible(True)

        # 执行查询
        try:
            logging.info(f"发起失信人API查询: '{keyword}'")
            result = self.api_client.query_dishonest_person(keyword)
            if result['success']:
                data = result['data']
                if data:
                    # 记录这是来自API的新数据（非缓存）
                    self.object_cache_status[keyword] = False

                    # 更新内存缓存
                    self.query_results[keyword] = data
                    self.current_result = data

                    # 缓存到持久化存储
                    self._cache_result(keyword, data)

                    self.update_result_display(data)
                    self.update_result_selector([keyword])

                    # 检查是否为"经查无结果"的成功响应
                    error_code = data.get('error_code')
                    if error_code == 300000:
                        self.status_label.setText("查询成功：该对象无失信记录")
                        QMessageBox.information(self, '查询结果', f'查询成功！\n\n"{keyword}" 无失信被执行人记录。')
                        logging.info(f"✅ 失信人查询成功（无记录）: '{keyword}'")
                    else:
                        self.status_label.setText("失信人查询成功")
                        logging.info(f"✅ 失信人查询成功: '{keyword}'")

                    self.update_cache_reminder(is_cached=False)  # 隐藏缓存提醒（实时数据）
                else:
                    self.status_label.setText("未找到失信人数据")
                    QMessageBox.warning(self, '查询结果', '未找到相关失信人信息')
                    logging.warning(f"失信人API返回空数据: '{keyword}'")
            else:
                self.status_label.setText(f"失信人查询失败: {result['message']}")
                QMessageBox.warning(self, '查询失败', result['message'])
                logging.warning(f"失信人API查询失败: '{keyword}' - {result['message']}")

        except Exception as e:
            error_msg = f"失信人查询过程中出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            self.status_label.setText("失信人查询出错")
            QMessageBox.critical(self, '错误', error_msg)

        finally:
            self.disable_controls(False)

    def show_cached_result(self, keyword):
        """显示缓存结果"""
        data = self.query_results.get(keyword)
        if data:
            # 记录缓存状态
            self.object_cache_status[keyword] = True

            self.current_result = data
            self.update_result_display(data)
            self.update_result_selector([keyword])
            self.status_label.setText("显示失信人缓存数据")
            self.status_label.setVisible(True)
            self.update_cache_reminder(is_cached=True)  # 显示缓存提醒

    def batch_query(self):
        """批量查询"""
        keywords = [kw.strip() for kw in self.batch_query_input.toPlainText().split('\n') if kw.strip()]

        logging.info(f"Batch query initiated with keywords: {keywords}")

        if not keywords:
            QMessageBox.warning(self, '警告', '请输入要查询的企业名称！')
            return

        # 如果只有一个关键词，直接使用单一查询功能
        if len(keywords) == 1:
            logging.info("Single keyword detected, using single query")
            self.query_input.setText(keywords[0])
            self.single_query()
            return

        logging.info(f"Starting batch query for {len(keywords)} keywords")

        # 初始化批量查询状态跟踪
        self.batch_query_errors = []  # 清空错误列表
        self.object_cache_status = {}  # 清空缓存状态跟踪

        # 创建带缓存检查的查询工作线程
        self.query_worker = DishonestQueryWorker(self.api_client, keywords, self.query_results)

        # 连接信号
        self.query_worker.progress_updated.connect(self.update_progress)
        self.query_worker.query_completed.connect(self.handle_query_result)
        self.query_worker.query_error.connect(self.handle_query_error)
        self.query_worker.status_updated.connect(lambda msg: self.status_label.setText(msg))
        self.query_worker.cache_prompt_needed.connect(self.handle_cache_prompt)
        self.query_worker.finished.connect(self.handle_batch_query_finished)

        logging.info("Signal connections established")

        # 清空显示结果
        self.result_display.clear()

        # 为批量查询创建临时结果存储
        self.batch_query_results = {}

        # 开始查询
        self.stop_batch_btn.setEnabled(True)
        logging.info("Starting worker thread...")
        self.query_worker.start()

    def handle_cache_prompt(self, keyword, cache_data):
        """处理缓存提示"""
        reply = QMessageBox.question(
            self, '缓存数据提示',
            f'查询"{keyword}"的失信人信息已在缓存中，是否使用缓存数据？\n\n'
            f'选择"是"：使用缓存数据（快速）\n'
            f'选择"否"：重新查询最新数据（较慢）',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes  # 默认选择使用缓存
        )

        use_cache = (reply == QMessageBox.Yes)

        # 通知工作线程用户的决定
        if self.query_worker:
            self.query_worker.handle_cache_decision(keyword, use_cache)

    def stop_batch_query(self):
        """停止批量查询"""
        if self.query_worker:
            self.query_worker.stop()
            self.status_label.setText("正在停止失信人查询...")

    def update_progress(self, current, total):
        """更新进度条"""
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)

    def handle_query_result(self, result):
        """处理查询结果"""
        keyword = result['keyword']
        data = result['data']
        from_cache = result.get('from_cache', False)

        # 记录该对象的缓存状态
        self.object_cache_status[keyword] = from_cache

        # 只有非缓存数据才添加到主缓存
        if not from_cache:
            self._cache_result(keyword, data)

        # 为批量查询结果显示添加到临时存储
        if hasattr(self, 'batch_query_results'):
            self.batch_query_results[keyword] = data

            # 显示结果到结果区域
            error_code = data.get('error_code')
            if error_code == 300000:
                self.result_display.append(f"✅ {keyword}: 查询成功（无失信记录）")
            else:
                self.result_display.append(f"✅ {keyword}: 查询成功")

            # 更新结果选择器显示批量查询结果
            current_keywords = list(self.batch_query_results.keys())
            self.update_result_selector(current_keywords)

            # 确保结果选择器可见（如果有多个结果）
            if len(current_keywords) > 1:
                self.result_selector.setVisible(True)
                # 设置当前选中项为最新查询的结果
                index = self.result_selector.findText(keyword)
                if index >= 0:
                    self.result_selector.setCurrentIndex(index)

            # 更新当前显示的结果
            self.current_result = data
            self.update_result_display(data)
            # 根据数据来源设置缓存提醒
            self.update_cache_reminder(is_cached=from_cache)
        else:
            # 单一查询的情况
            self.query_results[keyword] = data
            self.current_result = data
            self.update_result_display(data)
            self.update_result_selector([keyword])
            # 根据数据来源设置缓存提醒
            self.update_cache_reminder(is_cached=from_cache)

        logging.info(f"✅ 失信人查询成功: '{keyword}' (来源: {'缓存' if from_cache else 'API'})")

    def handle_query_error(self, error_msg):
        """处理查询错误 - 改进版：减少弹窗干扰，统一在最后汇总显示"""
        # 记录错误到错误列表
        self.batch_query_errors.append(error_msg)

        # 显示错误到结果区域
        self.result_display.append(f"❌ {error_msg}")
        self.status_label.setText(error_msg)

        # 不再为每个错误显示弹窗，而是在批量查询完成后统一汇总显示
        logging.error(f"批量查询错误: {error_msg}")

    def handle_batch_query_finished(self):
        """处理批量查询完成 - 改进版：更准确的统计和更好的用户体验"""
        try:
            logging.info("Batch query finished handler called")

            # 重置控件状态
            self.stop_batch_btn.setEnabled(False)
            self.progress_bar.setVisible(False)

            # 统计结果 - 更准确的计算
            successful_queries = len(self.batch_query_results) if hasattr(self, 'batch_query_results') else 0
            failed_queries = len(self.batch_query_errors)
            total_queries = successful_queries + failed_queries

            logging.info(f"Batch query statistics: successful={successful_queries}, failed={failed_queries}, total={total_queries}")

            # 更新状态标签
            if failed_queries == 0:
                self.status_label.setText(f"✅ 批量查询完成 - 全部成功 ({successful_queries}/{total_queries})")
            else:
                self.status_label.setText(f"⚠️ 批量查询完成 - 成功: {successful_queries}, 失败: {failed_queries}")

            # 显示完成通知（包含错误汇总）
            if failed_queries == 0:
                # 全部成功
                QMessageBox.information(
                    self,
                    '批量查询完成',
                    f'🎉 批量查询全部完成！\n\n'
                    f'✅ 成功查询: {successful_queries} 个\n'
                    f'❌ 失败查询: 0 个\n\n'
                    f'所有查询结果已保存到缓存中。',
                    QMessageBox.Ok
                )
            else:
                # 有失败的查询
                error_summary = f"批量查询完成，结果汇总：\n\n"
                error_summary += f"✅ 成功查询: {successful_queries} 个\n"
                error_summary += f"❌ 失败查询: {failed_queries} 个\n\n"

                if failed_queries <= 5:
                    # 失败数量较少，显示详细错误
                    error_summary += "失败详情：\n"
                    for i, error in enumerate(self.batch_query_errors, 1):
                        error_summary += f"{i}. {error}\n"
                else:
                    # 失败数量较多，只显示前几个
                    error_summary += "失败详情（前5个）：\n"
                    for i, error in enumerate(self.batch_query_errors[:5], 1):
                        error_summary += f"{i}. {error}\n"
                    error_summary += f"... 还有 {failed_queries - 5} 个失败查询\n"

                error_summary += f"\n成功的查询结果已保存到缓存中。"

                QMessageBox.warning(
                    self,
                    '批量查询结果汇总',
                    error_summary,
                    QMessageBox.Ok
                )

            logging.info(f"批量查询完成 - 成功: {successful_queries}, 失败: {failed_queries}")

        except Exception as e:
            logging.error(f"处理批量查询完成时出错: {str(e)}", exc_info=True)

    def disable_controls(self, disabled):
        """禁用/启用控件"""
        self.query_input.setEnabled(not disabled)
        self.batch_query_input.setEnabled(not disabled)
        self.progress_bar.setVisible(disabled)
        self.stop_batch_btn.setEnabled(disabled)

    def update_result_selector(self, keywords):
        """更新结果选择器"""
        self.result_selector.clear()
        if len(keywords) > 1:
            self.result_selector.setVisible(True)
            self.result_selector.addItems(keywords)
        else:
            self.result_selector.setVisible(False)

    def show_selected_result(self, index):
        """显示选中的结果 - 修复版：正确跟踪缓存状态"""
        if index >= 0:
            keyword = self.result_selector.itemText(index)

            # 检查是否为历史查询结果
            if keyword.endswith('（历史）'):
                # 从历史查询结果中获取数据
                original_keyword = keyword[:-4]  # 移除"（历史）"后缀
                data = getattr(self, 'historical_query_results', {}).get(original_keyword)
                # 历史查询结果不显示缓存提醒，因为它们总是最新的API数据
                is_cached = False
            else:
                # 从普通查询结果中获取数据
                data = None
                is_cached = False

                # 优先从批量查询结果中获取数据
                if hasattr(self, 'batch_query_results') and keyword in self.batch_query_results:
                    data = self.batch_query_results[keyword]
                    # 使用记录的缓存状态
                    is_cached = self.object_cache_status.get(keyword, False)
                # 如果批量查询结果中没有，从主缓存中获取
                elif keyword in self.query_results:
                    data = self.query_results[keyword]
                    # 从主缓存获取的数据总是被视为缓存数据
                    is_cached = True

            if data:
                self.current_result = data
                self.update_result_display(data)
                self.update_cache_reminder(is_cached=is_cached)
                logging.info(f"显示结果: '{keyword}', 缓存状态: {is_cached}")

    def update_result_display(self, data):
        """更新结果显示"""
        try:
            if not data or not isinstance(data, dict):
                self.result_display.setText("数据为空或格式无效")
                return

            # 使用数据处理器格式化显示
            formatted_text = self.data_processor.format_for_display(data)
            self.result_display.setText(formatted_text)

        except Exception as e:
            error_msg = f"显示失信人数据时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            self.result_display.setText(error_msg)

    def export_to_excel(self):
        """导出数据到Excel"""
        try:
            # 检查是否有批量查询结果或多个结果项（包括历史查询）
            has_batch_results = hasattr(self, 'batch_query_results') and self.batch_query_results
            has_multiple_results = (self.result_selector.isVisible() and
                                  self.result_selector.count() > 1)

            if has_batch_results or has_multiple_results:
                # 批量查询或多个结果：导出所有结果到不同工作表
                self._export_batch_results()
            elif self.current_result:
                # 单一查询：导出当前结果
                self._export_single_result()
            else:
                QMessageBox.warning(self, "警告", "没有可导出的失信人数据")

        except Exception as e:
            error_msg = f"导出失信人Excel时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            QMessageBox.critical(self, "错误", error_msg)

    def _export_single_result(self):
        """导出单一查询结果"""
        # 获取保存路径
        file_name = f"失信人信息_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        save_path, _ = QFileDialog.getSaveFileName(
            self, "保存Excel文件", file_name, "Excel 文件 (*.xlsx)"
        )

        if not save_path:
            return

        # 导出数据
        if self.data_processor.export_to_excel(self.current_result, save_path, is_batch=False):
            QMessageBox.information(self, "成功", "失信人数据导出成功！")
            # 打开文件所在文件夹
            os.startfile(os.path.dirname(save_path))
        else:
            QMessageBox.warning(self, "错误", "导出失败，请查看日志了解详细信息")

    def _export_batch_results(self):
        """导出批量查询结果"""
        # 获取保存路径
        file_name = f"失信人批量查询_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        save_path, _ = QFileDialog.getSaveFileName(
            self, "保存Excel文件", file_name, "Excel 文件 (*.xlsx)"
        )

        if not save_path:
            return

        # 获取下拉选择框中当前显示的所有项目
        current_selector_items = []
        if self.result_selector.isVisible():
            for i in range(self.result_selector.count()):
                item_text = self.result_selector.itemText(i)
                if item_text:
                    current_selector_items.append(item_text)

        # 根据下拉选择框中的项目收集对应的数据
        all_results = {}

        # 处理下拉选择框中的每个项目
        for item_text in current_selector_items:
            if item_text.endswith('（历史）'):
                # 历史查询结果
                original_keyword = item_text[:-4]  # 移除"（历史）"后缀
                if (hasattr(self, 'historical_query_results') and
                    self.historical_query_results and
                    original_keyword in self.historical_query_results):
                    all_results[item_text] = self.historical_query_results[original_keyword]
            else:
                # 普通查询结果
                # 首先检查批量查询结果
                if (hasattr(self, 'batch_query_results') and
                    self.batch_query_results and
                    item_text in self.batch_query_results):
                    all_results[item_text] = self.batch_query_results[item_text]

                # 如果在批量查询结果中没找到，检查单一查询结果
                elif (hasattr(self, 'query_results') and
                      self.query_results and
                      item_text in self.query_results):
                    all_results[item_text] = self.query_results[item_text]

        # 如果没有找到任何数据，回退到原有逻辑（向后兼容）
        if not all_results:
            # 添加批量查询结果
            if hasattr(self, 'batch_query_results') and self.batch_query_results:
                all_results.update(self.batch_query_results)

            # 添加单一查询结果（如果没有批量查询结果但有单一查询结果）
            elif hasattr(self, 'query_results') and self.query_results:
                all_results.update(self.query_results)

            # 添加历史查询结果（添加"（历史）"后缀到工作表名）
            if hasattr(self, 'historical_query_results') and self.historical_query_results:
                for keyword, data in self.historical_query_results.items():
                    sheet_name_key = f"{keyword}（历史）"
                    all_results[sheet_name_key] = data

        # 导出批量数据
        if self.data_processor.export_to_excel(all_results, save_path, is_batch=True):
            keyword_count = len(all_results)
            QMessageBox.information(
                self, "成功",
                f"失信人批量查询数据导出成功！\n共导出 {keyword_count} 个查询结果，每个结果为单独的工作表。"
            )
            # 打开文件所在文件夹
            os.startfile(os.path.dirname(save_path))
        else:
            QMessageBox.warning(self, "错误", "导出失败，请查看日志了解详细信息")

    def show_debug_json(self):
        """显示原始JSON数据（调试用）"""
        try:
            if not self.current_result:
                QMessageBox.warning(self, "调试", "没有可显示的失信人数据")
                return

            # 创建调试窗口
            debug_dialog = QDialog(self)
            debug_dialog.setWindowTitle('失信人原始JSON数据 - 调试')
            debug_dialog.setGeometry(200, 200, 800, 600)

            layout = QVBoxLayout()

            # 添加说明标签
            info_label = QLabel("以下是未经任何处理的失信人原始JSON数据：")
            layout.addWidget(info_label)

            # 添加JSON显示区域
            json_display = QTextEdit()
            json_display.setReadOnly(True)
            json_display.setFont(QFont("Consolas", 10))

            # 格式化JSON数据
            import json
            formatted_json = json.dumps(self.current_result, ensure_ascii=False, indent=2)
            json_display.setText(formatted_json)

            layout.addWidget(json_display)

            # 添加复制按钮
            copy_btn = QPushButton('复制到剪贴板')
            copy_btn.clicked.connect(lambda: self.copy_to_clipboard(formatted_json))
            layout.addWidget(copy_btn)

            # 添加关闭按钮
            close_btn = QPushButton('关闭')
            close_btn.clicked.connect(debug_dialog.close)
            layout.addWidget(close_btn)

            debug_dialog.setLayout(layout)
            debug_dialog.exec_()

        except Exception as e:
            error_msg = f"显示失信人调试信息时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            QMessageBox.critical(self, "错误", error_msg)

    def copy_to_clipboard(self, text):
        """复制文本到剪贴板"""
        try:
            from PyQt5.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(text)
            QMessageBox.information(self, "成功", "失信人JSON数据已复制到剪贴板")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"复制失败: {str(e)}")

    def query_historical_dishonest(self):
        """查询历史失信被执行人信息 - 总是查询最新数据，不使用缓存"""
        # 获取当前查询的关键词
        current_keyword = ""

        # 改进的关键词获取逻辑
        if self.result_selector.isVisible() and self.result_selector.count() > 0:
            # 如果结果选择器可见且有内容，使用当前选中的项
            current_index = self.result_selector.currentIndex()
            if current_index >= 0:
                current_keyword = self.result_selector.itemText(current_index)

        # 如果从选择器没有获取到关键词，尝试从输入框获取
        if not current_keyword:
            current_keyword = self.query_input.text().strip()

        # 如果还是没有关键词，检查是否有当前结果
        if not current_keyword and self.current_result:
            # 尝试从当前结果中提取企业名称
            try:
                if 'result' in self.current_result and 'items' in self.current_result['result']:
                    items = self.current_result['result']['items']
                    if items and len(items) > 0:
                        # 从第一个失信记录中获取企业名称
                        current_keyword = items[0].get('iname', '').replace('（历史）', '')
            except Exception as e:
                logging.warning(f"从当前结果提取关键词时出错: {str(e)}")

        if not current_keyword:
            QMessageBox.warning(self, '警告', '请先进行查询或选择一个查询结果！')
            return

        # 移除可能存在的"（历史）"后缀
        if current_keyword.endswith('（历史）'):
            current_keyword = current_keyword[:-4]

        logging.info(f"开始历史失信人查询: '{current_keyword}' (总是使用最新API数据)")

        self.disable_controls(True)
        self.status_label.setText(f"正在查询历史失信人: {current_keyword}")
        self.status_label.setVisible(True)

        # 执行历史查询 - 总是调用API，不检查缓存
        try:
            logging.info(f"发起历史失信人API查询: '{current_keyword}' (跳过缓存)")
            result = self.api_client.query_historical_dishonest_person(current_keyword)

            if result['success']:
                data = result['data']
                if data and data.get('result', {}).get('items'):
                    # 为历史数据添加标识
                    self._mark_as_historical_data(data)

                    # 存储历史查询结果（仅用于显示，不缓存到主缓存）
                    if not hasattr(self, 'historical_query_results'):
                        self.historical_query_results = {}
                    self.historical_query_results[current_keyword] = data
                    self.current_result = data

                    # 更新显示
                    self.update_result_display(data)

                    # 更新结果选择器，添加历史查询结果
                    self._update_result_selector_with_historical(current_keyword)

                    self.status_label.setText("历史失信人查询成功")
                    # 历史查询结果不显示缓存提醒，因为这是最新的API数据
                    self.update_cache_reminder(is_cached=False)
                    logging.info(f"✅ 历史失信人查询成功: '{current_keyword}' (使用最新API数据)")
                else:
                    self.status_label.setText("未找到历史失信人数据")
                    QMessageBox.information(self, '查询结果', '未找到相关历史失信人信息')
                    logging.warning(f"历史失信人API返回空数据: '{current_keyword}'")
            else:
                self.status_label.setText(f"历史失信人查询失败: {result['message']}")
                QMessageBox.warning(self, '查询失败', f"历史失信人查询失败: {result['message']}")
                logging.warning(f"历史失信人API查询失败: '{current_keyword}' - {result['message']}")

        except Exception as e:
            error_msg = f"历史失信人查询过程中出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            self.status_label.setText("历史失信人查询出错")
            QMessageBox.critical(self, '错误', error_msg)

        finally:
            self.disable_controls(False)

    def _mark_as_historical_data(self, data):
        """为历史数据添加标识"""
        if data and 'result' in data and 'items' in data['result']:
            for item in data['result']['items']:
                if 'iname' in item and item['iname']:
                    # 在失信人名称后添加"（历史）"标识
                    if not item['iname'].endswith('（历史）'):
                        item['iname'] = item['iname'] + '（历史）'

    def _update_result_selector_with_historical(self, keyword):
        """更新结果选择器，包含历史查询结果"""
        current_items = []

        # 添加普通查询结果
        if hasattr(self, 'batch_query_results') and self.batch_query_results:
            current_items.extend(list(self.batch_query_results.keys()))
        elif keyword in self.query_results:
            current_items.append(keyword)

        # 添加历史查询结果
        if hasattr(self, 'historical_query_results') and self.historical_query_results:
            for hist_keyword in self.historical_query_results.keys():
                hist_display_name = f"{hist_keyword}（历史）"
                if hist_display_name not in current_items:
                    current_items.append(hist_display_name)

        # 更新选择器
        if len(current_items) > 1:
            self.result_selector.clear()
            self.result_selector.addItems(current_items)
            self.result_selector.setVisible(True)

            # 选中历史查询结果
            hist_display_name = f"{keyword}（历史）"
            hist_index = current_items.index(hist_display_name) if hist_display_name in current_items else -1
            if hist_index >= 0:
                self.result_selector.setCurrentIndex(hist_index)
        else:
            self.result_selector.setVisible(False)


class MainWindow(QMainWindow):
    def __init__(self, api_client: APIClient):
        super().__init__()
        self.api_client = api_client
        self.config_manager = ConfigManager()
        self.data_processor = DataProcessor(self.config_manager.display_config)
        self.init_ui()
        self.setup_connections()

    def init_ui(self):
        self.setWindowTitle('联储证券企业信息查询系统')
        self.setGeometry(100, 100, 1000, 700)

        # 设置窗口图标
        window_icon = get_application_icon()
        if not window_icon.isNull():
            self.setWindowIcon(window_icon)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)

        # 创建选项卡
        self.tab_widget = QTabWidget()
        self.company_info_tab = CompanyInfoTab(self.api_client, self.config_manager)
        self.tab_widget.addTab(self.company_info_tab, '企业综合信息查询')

        # 添加失信被执行人查询选项卡
        self.dishonest_person_tab = DishonestPersonTab(self.api_client)
        self.tab_widget.addTab(self.dishonest_person_tab, '失信被执行人查询')

        # 连接选项卡切换信号
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

        main_layout.addWidget(self.tab_widget)

        # 添加配置组 - 根据选项卡显示不同内容
        self.config_group = QGroupBox("数据显示配置")
        self.config_layout = QGridLayout()
        self.config_layout.setSpacing(8)  # 设置控件间距
        self.config_layout.setContentsMargins(10, 10, 10, 10)  # 设置边距

        # 创建复选框并从配置中加载状态 - 包含所有可配置的部分
        self.checkboxes = {}

        # 从display_config.json中动态创建复选框，排除基本信息
        for section_name, section_config in self.config_manager.display_config.sections.items():
            if section_name == 'basic_info':  # 基本信息始终显示，不需要复选框
                continue

            title = section_config.get('title', section_name)
            checkbox = QCheckBox(f"显示{title}")
            self.checkboxes[section_name] = (f'显示{title}', checkbox)

        # 将复选框按多列排列以节省垂直空间
        checkbox_items = list(self.checkboxes.items())
        # 根据复选框数量动态确定列数，最多3列
        total_items = len(checkbox_items)
        if total_items <= 6:
            columns = 2  # 少量项目使用2列
        else:
            columns = 3  # 较多项目使用3列

        for i, (section_name, (label, checkbox)) in enumerate(checkbox_items):
            row = i // columns
            col = i % columns

            # 从配置中加载选中状态
            enabled = self.config_manager.display_config.sections.get(section_name, {}).get('enabled', False)
            checkbox.setChecked(enabled)

            # 添加到网格布局
            self.config_layout.addWidget(checkbox, row, col)

        self.config_group.setLayout(self.config_layout)

        # 创建失信人查询专用的历史查询按钮
        self.historical_query_group = QGroupBox("查询选项")
        historical_layout = QVBoxLayout()

        self.historical_query_btn = QPushButton("查询不到结果？点我查询历史失信被执行人信息")
        self.historical_query_btn.clicked.connect(self.dishonest_person_tab.query_historical_dishonest)
        self.historical_query_btn.setStyleSheet("""
            QPushButton {
                background-color: #e3f2fd;
                border: 2px solid #2196f3;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
                color: #1976d2;
            }
            QPushButton:hover {
                background-color: #bbdefb;
            }
            QPushButton:pressed {
                background-color: #90caf9;
            }
        """)

        historical_layout.addWidget(self.historical_query_btn)
        self.historical_query_group.setLayout(historical_layout)

        # 添加两个组到主布局
        main_layout.addWidget(self.config_group)
        main_layout.addWidget(self.historical_query_group)

        # 初始状态：显示企业查询配置，隐藏历史查询按钮
        self.historical_query_group.setVisible(False)

        # 添加状态栏
        self.statusBar().showMessage('就绪')

        # 添加版本信息到状态栏
        version_label = QLabel(f'版本：{VERSION}')
        self.statusBar().addPermanentWidget(version_label)

    def setup_connections(self):
        # 配置变更处理
        for section_name, (_, checkbox) in self.checkboxes.items():
            checkbox.stateChanged.connect(lambda state, name=section_name: self.update_config(name, bool(state)))

    def update_config(self, section_name: str, enabled: bool):
        """更新配置并保存"""
        try:
            self.config_manager.display_config.update_section_enabled(section_name, enabled)
            self.config_manager.save_config()

            # 更新数据处理器的配置
            if hasattr(self, 'company_info_tab'):
                self.company_info_tab.data_processor.update_config(self.config_manager.display_config)

                # 如果有当前数据，重新显示
                if self.company_info_tab.current_result:
                    self.company_info_tab.update_result_display(self.company_info_tab.current_result)

            self.statusBar().showMessage(f'已更新{self.checkboxes[section_name][0]}的显示设置', 3000)
        except Exception as e:
            self.statusBar().showMessage(f'更新配置失败: {str(e)}', 5000)
            logging.error(f'更新配置失败: {str(e)}', exc_info=True)

    def on_tab_changed(self, index):
        """选项卡切换时的处理"""
        try:
            if index == 0:  # 企业综合信息查询选项卡
                self.config_group.setVisible(True)
                self.historical_query_group.setVisible(False)
                self.statusBar().showMessage('已切换到企业综合信息查询', 2000)
            elif index == 1:  # 失信被执行人查询选项卡
                self.config_group.setVisible(False)
                self.historical_query_group.setVisible(True)
                self.statusBar().showMessage('已切换到失信被执行人查询', 2000)
        except Exception as e:
            logging.error(f'选项卡切换处理出错: {str(e)}', exc_info=True)


