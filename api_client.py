import json
import requests
from urllib.parse import quote

class APIClient:
    def __init__(self, token):
        self.token = token
        # 更新API地址和请求头格式
        self.base_url = 'http://open.api.tianyancha.com/services/open/cb/ic/2.0'
        self.headers = {
            'Authorization': token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }

    def query_company_info(self, keyword):
        """
        查询企业信息
        :param keyword: 搜索关键字（公司名称、公司id、注册号或统一社会信用代码）
        :return: JSON响应数据
        """
        try:
            # URL编码关键词
            encoded_keyword = quote(keyword)
            url = f"{self.base_url}?keyword={encoded_keyword}"

            # 发送请求
            response = requests.get(url, headers=self.headers, timeout=30)

            # 检查HTTP状态码
            if response.status_code != 200:
                # 提供更友好的错误信息
                if response.status_code == 401:
                    return {'success': False, 'message': 'API Token无效或未授权，请检查Token是否正确', 'data': None}
                elif response.status_code == 403:
                    return {'success': False, 'message': '访问被拒绝，请检查API权限或IP白名单', 'data': None}
                elif response.status_code == 418:
                    return {'success': False, 'message': '请求被服务器拦截，这可能是正常的防护机制，请稍后重试', 'data': None}
                elif response.status_code == 429:
                    return {'success': False, 'message': '请求频率过快，请稍后重试', 'data': None}
                else:
                    return {'success': False, 'message': f'HTTP错误 {response.status_code}: {response.reason}', 'data': None}

            # 获取响应文本
            response_text = response.text

            # 检查响应是否为空
            if not response_text.strip():
                return {'success': False, 'message': '服务器返回空响应', 'data': None}

            # 尝试解析JSON
            try:
                data = response.json()
            except json.JSONDecodeError as json_error:
                # 检查是否返回了HTML（通常表示Token问题）
                if response_text.strip().startswith('<'):
                    return {
                        'success': False,
                        'message': '服务器返回HTML页面而非数据，可能是Token无效或API地址错误',
                        'data': None
                    }
                else:
                    return {
                        'success': False,
                        'message': f'数据格式错误，无法解析服务器响应',
                        'data': None
                    }

            # 检查错误码
            error_code = data.get('error_code')
            if error_code is not None and error_code != 0:
                error_msg = self.get_error_message(error_code)
                return {'success': False, 'message': error_msg, 'data': None}

            # 包装响应数据
            return {'success': True, 'message': 'ok', 'data': data}

        except requests.exceptions.Timeout:
            return {'success': False, 'message': '请求超时，请检查网络连接', 'data': None}
        except requests.exceptions.ConnectionError:
            return {'success': False, 'message': '连接错误，请检查网络连接和API地址', 'data': None}
        except requests.exceptions.RequestException as e:
            return {'success': False, 'message': f'网络请求错误: {str(e)}', 'data': None}
        except Exception as e:
            return {'success': False, 'message': f'未知错误: {str(e)}', 'data': None}

    def test_connection(self):
        """测试API连接和Token有效性 - 宽松验证"""
        try:
            # 使用一个简单的测试关键词
            test_keyword = "测试"
            encoded_keyword = quote(test_keyword)
            url = f"{self.base_url}?keyword={encoded_keyword}"

            # 发送测试请求
            response = requests.get(url, headers=self.headers, timeout=10)

            # 只检查明确的失败情况，其他情况都认为可以尝试
            if response.status_code == 401:
                return {'success': False, 'message': 'Token无效或未授权'}
            elif response.status_code == 403:
                return {'success': False, 'message': '访问被拒绝，请检查Token权限'}

            # 对于其他状态码，尝试解析响应
            try:
                data = response.json()
                error_code = data.get('error_code')

                # 只有明确的账号问题才拒绝
                if error_code in [300002, 300003, 300005, 300009, 300011]:
                    error_msg = self.get_error_message(error_code)
                    return {'success': False, 'message': f'API错误: {error_msg}'}

                # 其他情况都认为连接正常，让用户尝试使用
                return {'success': True, 'message': 'API连接测试通过'}

            except json.JSONDecodeError:
                # 如果不是JSON，但状态码不是明确的错误，也让用户尝试
                if response.status_code in [200, 418]:  # 418可能是正常的防护机制
                    return {'success': True, 'message': 'API连接测试通过（响应格式特殊）'}
                else:
                    return {'success': False, 'message': f'HTTP错误: {response.status_code}'}

        except requests.exceptions.Timeout:
            return {'success': False, 'message': '连接超时，请检查网络'}
        except requests.exceptions.ConnectionError:
            return {'success': False, 'message': '网络连接错误，请检查网络设置'}
        except Exception as e:
            # 其他异常也让用户尝试，只是给出警告
            return {'success': True, 'message': f'连接测试异常但允许尝试: {str(e)}'}

    def query_dishonest_person(self, keyword, page_num=1, page_size=50):
        """
        查询失信被执行人信息
        :param keyword: 搜索关键字（公司名称、公司id、注册号或社会统一信用代码）
        :param page_num: 当前页数（默认第1页）
        :param page_size: 每页条数（默认50条，最大50条）
        :return: JSON响应数据
        """
        try:
            # 失信人查询API地址
            dishonest_url = 'http://open.api.tianyancha.com/services/open/jr/dishonest/2.0'

            # URL编码关键词
            encoded_keyword = quote(keyword)
            url = f"{dishonest_url}?keyword={encoded_keyword}&pageNum={page_num}&pageSize={page_size}"

            # 发送请求
            response = requests.get(url, headers=self.headers, timeout=30)

            # 检查HTTP状态码
            if response.status_code != 200:
                # 提供更友好的错误信息
                if response.status_code == 401:
                    return {'success': False, 'message': 'API Token无效或未授权，请检查Token是否正确', 'data': None}
                elif response.status_code == 403:
                    return {'success': False, 'message': '访问被拒绝，请检查API权限或IP白名单', 'data': None}
                elif response.status_code == 418:
                    return {'success': False, 'message': '请求被服务器拦截，这可能是正常的防护机制，请稍后重试', 'data': None}
                elif response.status_code == 429:
                    return {'success': False, 'message': '请求频率过快，请稍后重试', 'data': None}
                else:
                    return {'success': False, 'message': f'HTTP错误 {response.status_code}: {response.reason}', 'data': None}

            # 获取响应文本
            response_text = response.text

            # 检查响应是否为空
            if not response_text.strip():
                return {'success': False, 'message': '服务器返回空响应', 'data': None}

            # 尝试解析JSON
            try:
                data = response.json()
            except json.JSONDecodeError as json_error:
                # 检查是否返回了HTML（通常表示Token问题）
                if response_text.strip().startswith('<'):
                    return {
                        'success': False,
                        'message': '服务器返回HTML页面而非数据，可能是Token无效或API地址错误',
                        'data': None
                    }
                else:
                    return {
                        'success': False,
                        'message': f'数据格式错误，无法解析服务器响应',
                        'data': None
                    }

            # 检查错误码 - 对于失信人查询，300000表示"经查无结果"，应视为成功
            error_code = data.get('error_code')
            if error_code is not None and error_code != 0:
                # 对于失信人查询，300000（经查无结果）是有效的成功响应
                if error_code == 300000:
                    # 返回成功，但数据为空的结构，表示查询成功但无失信记录
                    return {
                        'success': True,
                        'message': '查询成功，该对象无失信记录',
                        'data': {
                            'error_code': 300000,
                            'reason': '经查无结果',
                            'result': {'items': [], 'total': 0}
                        }
                    }
                else:
                    # 其他错误码仍然视为失败
                    error_msg = self.get_error_message(error_code)
                    return {'success': False, 'message': error_msg, 'data': None}

            # 包装响应数据
            return {'success': True, 'message': 'ok', 'data': data}

        except requests.exceptions.Timeout:
            return {'success': False, 'message': '请求超时，请检查网络连接', 'data': None}
        except requests.exceptions.ConnectionError:
            return {'success': False, 'message': '连接错误，请检查网络连接和API地址', 'data': None}
        except requests.exceptions.RequestException as e:
            return {'success': False, 'message': f'网络请求错误: {str(e)}', 'data': None}
        except Exception as e:
            return {'success': False, 'message': f'未知错误: {str(e)}', 'data': None}

    def query_historical_dishonest_person(self, keyword, page_num=1, page_size=20):
        """
        查询历史失信被执行人信息
        :param keyword: 搜索关键字（公司名称、公司id、注册号或社会统一信用代码）
        :param page_num: 当前页数（默认第1页）
        :param page_size: 每页条数（默认20条，最大20条）
        :return: JSON响应数据
        """
        try:
            # 历史失信人查询API地址
            historical_url = 'http://open.api.tianyancha.com/services/open/hi/dishonest/2.0'

            # URL编码关键词
            encoded_keyword = quote(keyword)
            url = f"{historical_url}?keyword={encoded_keyword}&pageNum={page_num}&pageSize={page_size}"

            # 发送请求
            response = requests.get(url, headers=self.headers, timeout=30)

            # 检查HTTP状态码
            if response.status_code != 200:
                # 提供更友好的错误信息
                if response.status_code == 401:
                    return {'success': False, 'message': 'API Token无效或未授权，请检查Token是否正确', 'data': None}
                elif response.status_code == 403:
                    return {'success': False, 'message': '访问被拒绝，请检查API权限或IP白名单', 'data': None}
                elif response.status_code == 418:
                    return {'success': False, 'message': '请求被服务器拦截，这可能是正常的防护机制，请稍后重试', 'data': None}
                elif response.status_code == 429:
                    return {'success': False, 'message': '请求频率过快，请稍后重试', 'data': None}
                else:
                    return {'success': False, 'message': f'HTTP错误 {response.status_code}: {response.reason}', 'data': None}

            # 获取响应文本
            response_text = response.text

            # 检查响应是否为空
            if not response_text.strip():
                return {'success': False, 'message': '服务器返回空响应', 'data': None}

            # 尝试解析JSON
            try:
                data = response.json()
            except json.JSONDecodeError as json_error:
                # 检查是否返回了HTML（通常表示Token问题）
                if response_text.strip().startswith('<'):
                    return {
                        'success': False,
                        'message': '服务器返回HTML页面而非数据，可能是Token无效或API地址错误',
                        'data': None
                    }
                else:
                    return {
                        'success': False,
                        'message': f'数据格式错误，无法解析服务器响应',
                        'data': None
                    }

            # 检查错误码 - 对于历史失信人查询，300000表示"经查无结果"，应视为成功
            error_code = data.get('error_code')
            if error_code is not None and error_code != 0:
                # 对于历史失信人查询，300000（经查无结果）是有效的成功响应
                if error_code == 300000:
                    # 返回成功，但数据为空的结构，表示查询成功但无历史失信记录
                    return {
                        'success': True,
                        'message': '查询成功，该对象无历史失信记录',
                        'data': {
                            'error_code': 300000,
                            'reason': '经查无结果',
                            'result': {'items': [], 'total': 0}
                        }
                    }
                else:
                    # 其他错误码仍然视为失败
                    error_msg = self.get_error_message(error_code)
                    return {'success': False, 'message': error_msg, 'data': None}

            # 包装响应数据
            return {'success': True, 'message': 'ok', 'data': data}

        except requests.exceptions.Timeout:
            return {'success': False, 'message': '请求超时，请检查网络连接', 'data': None}
        except requests.exceptions.ConnectionError:
            return {'success': False, 'message': '连接错误，请检查网络连接和API地址', 'data': None}
        except requests.exceptions.RequestException as e:
            return {'success': False, 'message': f'网络请求错误: {str(e)}', 'data': None}
        except Exception as e:
            return {'success': False, 'message': f'未知错误: {str(e)}', 'data': None}

    @staticmethod
    def get_error_message(error_code):
        """
        获取错误码对应的错误信息
        """
        error_codes = {
            300000: '经查无结果',
            300001: '请求失败',
            300002: '账号失效',
            300003: '账号过期',
            300004: '访问频率过快',
            300005: '无权限访问此api',
            300006: '余额不足',
            300007: '剩余次数不足',
            300008: '缺少必要参数',
            300009: '账号信息有误',
            300010: 'URL不存在',
            300011: '此IP无权限访问此api',
            300012: '报告生成中'
        }
        return error_codes.get(error_code, '未知错误')
