2025-07-11 10:15:47,456 [INFO] 应用程序启动
2025-07-11 10:15:47,568 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 10:15:47,569 [INFO] 应用程序图标已设置
2025-07-11 10:15:47,575 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 10:15:58,176 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 10:15:58,179 [INFO] 开始加载缓存文件: company_cache.dat
2025-07-11 10:15:58,209 [INFO] 缓存文件包含 0 个条目
2025-07-11 10:15:58,210 [INFO] 缓存加载统计: 总计 0 个条目 (新格式: 0, 旧格式: 0, 错误: 0)
2025-07-11 10:15:58,231 [INFO] 缓存加载完成，共 0 个查询条目
2025-07-11 10:15:58,261 [INFO] 开始加载失信人缓存文件: dishonest_cache.dat
2025-07-11 10:15:58,262 [INFO] 失信人缓存文件包含 0 个条目
2025-07-11 10:15:58,263 [INFO] 失信人缓存加载完成，共 0 个查询条目
2025-07-11 11:03:25,167 [INFO] 开始失信人查询: '联储证券股份有限公司'
2025-07-11 11:03:25,168 [INFO] 发起失信人API查询: '联储证券股份有限公司'
2025-07-11 11:15:46,486 [WARNING] 失信人API查询失败: '联储证券股份有限公司' - 账号信息有误
2025-07-11 14:28:02,847 [INFO] 应用程序启动
2025-07-11 14:28:02,958 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 14:28:02,958 [INFO] 应用程序图标已设置
2025-07-11 14:28:02,961 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 14:28:23,808 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 14:28:23,820 [INFO] 开始加载缓存文件: company_cache.dat
2025-07-11 14:28:23,840 [INFO] 缓存文件包含 0 个条目
2025-07-11 14:28:23,840 [INFO] 缓存加载统计: 总计 0 个条目 (新格式: 0, 旧格式: 0, 错误: 0)
2025-07-11 14:28:23,841 [INFO] 缓存加载完成，共 0 个查询条目
2025-07-11 14:28:23,860 [INFO] 开始加载失信人缓存文件: dishonest_cache.dat
2025-07-11 14:28:23,862 [INFO] 失信人缓存文件包含 0 个条目
2025-07-11 14:28:23,862 [INFO] 失信人缓存加载完成，共 0 个查询条目
2025-07-11 14:32:46,661 [INFO] 开始缓存失信人查询结果，查询字符串: '联储证券股份有限公司'
2025-07-11 14:32:46,661 [INFO] 使用失信人缓存键: '联储证券股份有限公司'
2025-07-11 14:32:46,667 [INFO] 已保存到失信人持久化缓存: '联储证券股份有限公司'
2025-07-11 14:32:46,679 [INFO] 已添加到失信人内存缓存: '联储证券股份有限公司'
2025-07-11 14:33:07,038 [INFO] 批量查询完成 - 成功: 1, 失败: 0
2025-07-11 14:33:18,422 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 14:33:22,682 [INFO] 开始缓存失信人查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-11 14:33:22,682 [INFO] 使用失信人缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-11 14:33:22,688 [INFO] 已保存到失信人持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-11 14:33:22,705 [INFO] 已添加到失信人内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-11 14:33:22,706 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 14:33:24,864 [INFO] 显示结果: '北京远翰国际教育咨询有限责任公司', 缓存状态: False
2025-07-11 14:33:36,400 [INFO] 开始缓存查询结果，查询字符串: '联储证券股份有限公司'
2025-07-11 14:33:36,400 [INFO] 使用缓存键: '联储证券股份有限公司'
2025-07-11 14:33:36,413 [INFO] 已保存到持久化缓存: '联储证券股份有限公司'
2025-07-11 14:33:36,436 [INFO] 已添加到内存缓存: '联储证券股份有限公司'
2025-07-11 14:33:36,436 [INFO] ✅ 缓存完成: 查询'联储证券股份有限公司' -> 企业(信用代码: 91440300727132290A)
2025-07-11 14:33:36,438 [INFO] ✅ 验证成功: '联储证券股份有限公司' 已存在于持久化缓存中
2025-07-11 14:33:37,612 [INFO] 开始缓存查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-11 14:33:37,613 [INFO] 使用缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-11 14:33:37,617 [INFO] 已保存到持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-11 14:33:37,636 [INFO] 已添加到内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-11 14:33:37,636 [INFO] ✅ 缓存完成: 查询'北京远翰国际教育咨询有限责任公司' -> 企业(信用代码: 91110107551400801W)
2025-07-11 14:33:37,639 [INFO] ✅ 验证成功: '北京远翰国际教育咨询有限责任公司' 已存在于持久化缓存中
2025-07-11 14:33:57,495 [INFO] 开始批量导出 2 个企业到Excel
2025-07-11 14:33:57,537 [INFO] 成功处理企业: 联储证券股份有限公司 -> 联储证券股份有限公司
2025-07-11 14:33:57,540 [INFO] 成功处理企业: 北京远翰国际教育咨询有限责任公司 -> 北京远翰国际教育咨询有限责任公司
2025-07-11 14:33:57,566 [INFO] 批量导出完成: C:/Users/<USER>/Desktop/企业信息批量导出_20250711_143354.xlsx, 共 2 行数据
2025-07-11 14:46:20,017 [INFO] 开始缓存失信人查询结果，查询字符串: '联储证券股份有限公司'
2025-07-11 14:46:20,018 [INFO] 使用失信人缓存键: '联储证券股份有限公司'
2025-07-11 14:46:20,037 [INFO] 已保存到失信人持久化缓存: '联储证券股份有限公司'
2025-07-11 14:46:20,075 [INFO] 已添加到失信人内存缓存: '联储证券股份有限公司'
2025-07-11 14:47:01,009 [INFO] 批量查询完成 - 成功: 1, 失败: 0
2025-07-11 14:49:07,725 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 14:49:10,755 [INFO] 开始缓存失信人查询结果，查询字符串: '中国农业银行股份有限公司'
2025-07-11 14:49:10,755 [INFO] 使用失信人缓存键: '中国农业银行股份有限公司'
2025-07-11 14:49:10,761 [INFO] 已保存到失信人持久化缓存: '中国农业银行股份有限公司'
2025-07-11 14:49:10,771 [INFO] 已添加到失信人内存缓存: '中国农业银行股份有限公司'
2025-07-11 14:49:10,772 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 14:49:13,069 [INFO] 显示结果: '中国农业银行股份有限公司', 缓存状态: False
2025-07-11 14:49:14,834 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 14:49:15,957 [INFO] 显示结果: '中国农业银行股份有限公司', 缓存状态: False
2025-07-11 14:49:17,716 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 14:49:19,117 [INFO] 显示结果: '中国农业银行股份有限公司', 缓存状态: False
2025-07-11 14:50:23,141 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 14:50:28,280 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 14:50:29,829 [INFO] 显示结果: '中国农业银行股份有限公司', 缓存状态: True
2025-07-11 14:50:31,006 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 14:50:32,931 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 14:50:39,414 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 14:50:40,818 [INFO] 显示结果: '中国农业银行股份有限公司', 缓存状态: True
2025-07-11 14:50:42,044 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 14:50:43,278 [INFO] 显示结果: '中国农业银行股份有限公司', 缓存状态: True
2025-07-11 14:50:44,163 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 14:51:27,268 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 14:51:32,495 [INFO] 开始缓存失信人查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-11 14:51:32,496 [INFO] 使用失信人缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-11 14:51:32,507 [INFO] 已保存到失信人持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-11 14:51:32,522 [INFO] 已添加到失信人内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-11 14:51:32,523 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 14:51:34,192 [INFO] 显示结果: '北京远翰国际教育咨询有限责任公司', 缓存状态: False
2025-07-11 14:51:37,251 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 14:51:42,601 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 14:51:46,118 [INFO] 显示结果: '北京远翰国际教育咨询有限责任公司', 缓存状态: True
2025-07-11 14:51:54,601 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 14:53:11,994 [INFO] 开始缓存失信人查询结果，查询字符串: '联储证券股份有限公司'
2025-07-11 14:53:11,995 [INFO] 使用失信人缓存键: '联储证券股份有限公司'
2025-07-11 14:53:12,012 [INFO] 已保存到失信人持久化缓存: '联储证券股份有限公司'
2025-07-11 14:53:12,035 [INFO] 已添加到失信人内存缓存: '联储证券股份有限公司'
2025-07-11 14:54:23,420 [INFO] 批量查询完成 - 成功: 1, 失败: 0
2025-07-11 14:57:14,364 [INFO] 开始缓存失信人查询结果，查询字符串: '联储证券股份有限公司'
2025-07-11 14:57:14,365 [INFO] 使用失信人缓存键: '联储证券股份有限公司'
2025-07-11 14:57:14,383 [INFO] 已保存到失信人持久化缓存: '联储证券股份有限公司'
2025-07-11 14:57:14,425 [INFO] 已添加到失信人内存缓存: '联储证券股份有限公司'
2025-07-11 14:57:50,279 [INFO] 批量查询完成 - 成功: 1, 失败: 0
2025-07-11 14:57:57,863 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 14:58:00,363 [INFO] 开始缓存失信人查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-11 14:58:00,364 [INFO] 使用失信人缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-11 14:58:00,379 [INFO] 已保存到失信人持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-11 14:58:00,397 [INFO] 已添加到失信人内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-11 14:58:00,398 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 14:58:01,503 [INFO] 开始缓存失信人查询结果，查询字符串: '中国农业银行股份有限公司'
2025-07-11 14:58:01,504 [INFO] 使用失信人缓存键: '中国农业银行股份有限公司'
2025-07-11 14:58:01,509 [INFO] 已保存到失信人持久化缓存: '中国农业银行股份有限公司'
2025-07-11 14:58:01,520 [INFO] 已添加到失信人内存缓存: '中国农业银行股份有限公司'
2025-07-11 14:58:01,522 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 14:58:05,004 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 14:58:08,351 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 14:58:09,882 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 14:58:13,142 [INFO] 开始历史失信人查询: '联储证券股份有限公司' (总是使用最新API数据)
2025-07-11 14:58:13,143 [INFO] 发起历史失信人API查询: '联储证券股份有限公司' (跳过缓存)
2025-07-11 14:58:16,049 [WARNING] 历史失信人API返回空数据: '联储证券股份有限公司'
2025-07-11 15:06:55,230 [INFO] 开始缓存失信人查询结果，查询字符串: '联储证券股份有限公司'
2025-07-11 15:06:55,231 [INFO] 使用失信人缓存键: '联储证券股份有限公司'
2025-07-11 15:06:55,235 [INFO] 已保存到失信人持久化缓存: '联储证券股份有限公司'
2025-07-11 15:06:55,243 [INFO] 已添加到失信人内存缓存: '联储证券股份有限公司'
2025-07-11 15:06:56,176 [INFO] 批量查询完成 - 成功: 1, 失败: 0
2025-07-11 15:07:03,403 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 15:07:06,817 [INFO] 开始缓存失信人查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-11 15:07:06,818 [INFO] 使用失信人缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-11 15:07:06,821 [INFO] 已保存到失信人持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-11 15:07:06,834 [INFO] 已添加到失信人内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-11 15:07:06,835 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 15:07:07,952 [INFO] 开始缓存失信人查询结果，查询字符串: '中国农业银行股份有限公司'
2025-07-11 15:07:07,953 [INFO] 使用失信人缓存键: '中国农业银行股份有限公司'
2025-07-11 15:07:07,958 [INFO] 已保存到失信人持久化缓存: '中国农业银行股份有限公司'
2025-07-11 15:07:07,968 [INFO] 已添加到失信人内存缓存: '中国农业银行股份有限公司'
2025-07-11 15:07:07,969 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 15:07:10,053 [INFO] 显示结果: '中国农业银行股份有限公司', 缓存状态: False
2025-07-11 15:08:33,473 [INFO] 应用程序退出，退出代码：0
2025-07-11 15:49:27,356 [INFO] 应用程序启动
2025-07-11 15:49:27,445 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 15:49:27,445 [INFO] 应用程序图标已设置
2025-07-11 15:49:27,447 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 15:49:50,117 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 15:49:50,120 [INFO] 开始加载缓存文件: company_cache.dat
2025-07-11 15:49:50,132 [INFO] 缓存文件包含 0 个条目
2025-07-11 15:49:50,132 [INFO] 缓存加载统计: 总计 0 个条目 (新格式: 0, 旧格式: 0, 错误: 0)
2025-07-11 15:49:50,132 [INFO] 缓存加载完成，共 0 个查询条目
2025-07-11 15:49:50,158 [INFO] 开始加载失信人缓存文件: dishonest_cache.dat
2025-07-11 15:49:50,160 [INFO] 失信人缓存文件包含 0 个条目
2025-07-11 15:49:50,160 [INFO] 失信人缓存加载完成，共 0 个查询条目
2025-07-11 15:53:23,138 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 15:53:29,877 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 15:53:42,771 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 15:53:47,156 [INFO] 开始缓存查询结果，查询字符串: '联储证券股份有限公司'
2025-07-11 15:53:47,156 [INFO] 使用缓存键: '联储证券股份有限公司'
2025-07-11 15:53:47,163 [INFO] 已保存到持久化缓存: '联储证券股份有限公司'
2025-07-11 15:53:47,173 [INFO] 已添加到内存缓存: '联储证券股份有限公司'
2025-07-11 15:53:47,173 [INFO] ✅ 缓存完成: 查询'联储证券股份有限公司' -> 企业(信用代码: 91440300727132290A)
2025-07-11 15:53:47,175 [INFO] ✅ 验证成功: '联储证券股份有限公司' 已存在于持久化缓存中
2025-07-11 15:53:48,377 [INFO] 开始缓存查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-11 15:53:48,378 [INFO] 使用缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-11 15:53:48,383 [INFO] 已保存到持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-11 15:53:48,394 [INFO] 已添加到内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-11 15:53:48,394 [INFO] ✅ 缓存完成: 查询'北京远翰国际教育咨询有限责任公司' -> 企业(信用代码: 91110107551400801W)
2025-07-11 15:53:48,397 [INFO] ✅ 验证成功: '北京远翰国际教育咨询有限责任公司' 已存在于持久化缓存中
2025-07-11 15:53:59,315 [INFO] 开始批量导出 2 个企业到Excel，使用显示配置
2025-07-11 15:53:59,315 [INFO] 生成了 88 个导出列
2025-07-11 15:53:59,318 [INFO] 成功处理企业: 联储证券股份有限公司 -> 联储证券股份有限公司
2025-07-11 15:53:59,319 [INFO] 成功处理企业: 北京远翰国际教育咨询有限责任公司 -> 北京远翰国际教育咨询有限责任公司
2025-07-11 15:53:59,353 [INFO] 批量导出完成: C:/Users/<USER>/Desktop/企业信息批量导出_20250711_155356.xlsx, 共 2 行数据
2025-07-11 15:54:59,500 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 15:55:08,667 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 15:55:12,983 [INFO] 开始失信人查询: '联储证券股份有限公司'
2025-07-11 15:55:12,985 [INFO] 发起失信人API查询: '联储证券股份有限公司'
2025-07-11 15:55:13,210 [INFO] 开始缓存失信人查询结果，查询字符串: '联储证券股份有限公司'
2025-07-11 15:55:13,210 [INFO] 使用失信人缓存键: '联储证券股份有限公司'
2025-07-11 15:55:13,214 [INFO] 已保存到失信人持久化缓存: '联储证券股份有限公司'
2025-07-11 15:55:13,223 [INFO] 已添加到失信人内存缓存: '联储证券股份有限公司'
2025-07-11 15:55:15,908 [INFO] ✅ 失信人查询成功（无记录）: '联储证券股份有限公司'
2025-07-11 15:55:19,020 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 15:55:57,364 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 15:57:56,678 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 16:07:40,793 [INFO] 应用程序退出，退出代码：0
2025-07-11 16:07:48,383 [INFO] 应用程序启动
2025-07-11 16:07:48,485 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 16:07:48,485 [INFO] 应用程序图标已设置
2025-07-11 16:07:48,488 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 16:08:03,174 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 16:08:03,177 [INFO] 开始加载缓存文件: company_cache.dat
2025-07-11 16:08:03,185 [INFO] 缓存文件包含 0 个条目
2025-07-11 16:08:03,185 [INFO] 缓存加载统计: 总计 0 个条目 (新格式: 0, 旧格式: 0, 错误: 0)
2025-07-11 16:08:03,186 [INFO] 缓存加载完成，共 0 个查询条目
2025-07-11 16:08:03,204 [INFO] 开始加载失信人缓存文件: dishonest_cache.dat
2025-07-11 16:08:03,207 [INFO] 失信人缓存文件包含 1 个条目
2025-07-11 16:08:03,207 [INFO] 加载失信人缓存: '联储证券股份有限公司'
2025-07-11 16:08:03,208 [INFO] 失信人缓存加载完成，共 1 个查询条目
2025-07-11 16:08:12,801 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 16:08:17,566 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 16:08:21,092 [INFO] 应用程序退出，退出代码：0
2025-07-11 16:08:26,884 [INFO] 应用程序启动
2025-07-11 16:08:27,011 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 16:08:27,012 [INFO] 应用程序图标已设置
2025-07-11 16:08:27,014 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 16:08:32,424 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 16:08:32,427 [INFO] 开始加载缓存文件: company_cache.dat
2025-07-11 16:08:32,434 [INFO] 缓存文件包含 0 个条目
2025-07-11 16:08:32,435 [INFO] 缓存加载统计: 总计 0 个条目 (新格式: 0, 旧格式: 0, 错误: 0)
2025-07-11 16:08:32,435 [INFO] 缓存加载完成，共 0 个查询条目
2025-07-11 16:08:32,454 [INFO] 开始加载失信人缓存文件: dishonest_cache.dat
2025-07-11 16:08:32,457 [INFO] 失信人缓存文件包含 1 个条目
2025-07-11 16:08:32,458 [INFO] 加载失信人缓存: '联储证券股份有限公司'
2025-07-11 16:08:32,458 [INFO] 失信人缓存加载完成，共 1 个查询条目
2025-07-11 16:08:40,430 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 16:08:48,121 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 16:08:49,204 [INFO] 应用程序退出，退出代码：0
2025-07-11 16:11:20,665 [INFO] 应用程序启动
2025-07-11 16:11:20,769 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 16:11:20,770 [INFO] 应用程序图标已设置
2025-07-11 16:11:20,772 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 16:11:46,743 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 16:11:46,749 [INFO] 开始加载缓存文件: company_cache.dat
2025-07-11 16:11:46,763 [INFO] 缓存文件包含 0 个条目
2025-07-11 16:11:46,763 [INFO] 缓存加载统计: 总计 0 个条目 (新格式: 0, 旧格式: 0, 错误: 0)
2025-07-11 16:11:46,764 [INFO] 缓存加载完成，共 0 个查询条目
2025-07-11 16:11:46,785 [INFO] 开始加载失信人缓存文件: dishonest_cache.dat
2025-07-11 16:11:46,788 [INFO] 失信人缓存文件包含 1 个条目
2025-07-11 16:11:46,789 [INFO] 加载失信人缓存: '联储证券股份有限公司'
2025-07-11 16:11:46,790 [INFO] 失信人缓存加载完成，共 1 个查询条目
2025-07-11 16:16:56,223 [INFO] 应用程序启动
2025-07-11 16:16:56,316 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 16:16:56,316 [INFO] 应用程序图标已设置
2025-07-11 16:16:56,317 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 16:20:36,867 [INFO] 应用程序启动
2025-07-11 16:20:36,980 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 16:20:36,981 [INFO] 应用程序图标已设置
2025-07-11 16:20:36,983 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 16:20:43,224 [INFO] 成功加载base64编码的应用程序图标
2025-07-11 16:20:43,234 [INFO] 开始加载缓存文件: company_cache.dat
2025-07-11 16:20:43,260 [INFO] 缓存文件包含 0 个条目
2025-07-11 16:20:43,260 [INFO] 缓存加载统计: 总计 0 个条目 (新格式: 0, 旧格式: 0, 错误: 0)
2025-07-11 16:20:43,260 [INFO] 缓存加载完成，共 0 个查询条目
2025-07-11 16:20:43,280 [INFO] 开始加载失信人缓存文件: dishonest_cache.dat
2025-07-11 16:20:43,282 [INFO] 失信人缓存文件包含 1 个条目
2025-07-11 16:20:43,283 [INFO] 加载失信人缓存: '联储证券股份有限公司'
2025-07-11 16:20:43,283 [INFO] 失信人缓存加载完成，共 1 个查询条目
2025-07-11 16:20:49,597 [INFO] _process_next_query called: is_running=True, current_index=0, pending_count=2
2025-07-11 16:20:49,597 [INFO] Processing query 1/2: 联储证券股份有限公司
2025-07-11 16:20:53,049 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 16:20:54,712 [INFO] ✅ 失信人查询成功: '联储证券股份有限公司' (来源: 缓存)
2025-07-11 16:20:54,712 [INFO] Moving to next query: index now 1/2
2025-07-11 16:20:55,713 [INFO] _process_next_query called: is_running=True, current_index=1, pending_count=2
2025-07-11 16:20:55,713 [INFO] Processing query 2/2: 北京远翰国际教育咨询有限责任公司
2025-07-11 16:20:56,098 [INFO] 开始缓存失信人查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-11 16:20:56,098 [INFO] 使用失信人缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-11 16:20:56,104 [INFO] 已保存到失信人持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-11 16:20:56,123 [INFO] 已添加到失信人内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-11 16:20:56,124 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 16:20:56,143 [INFO] 显示结果: '北京远翰国际教育咨询有限责任公司', 缓存状态: False
2025-07-11 16:20:56,150 [INFO] ✅ 失信人查询成功: '北京远翰国际教育咨询有限责任公司' (来源: API)
2025-07-11 16:20:56,151 [INFO] Moving to next query: index now 2/2
2025-07-11 16:20:56,151 [INFO] All queries completed, will finish on next _process_next_query call
2025-07-11 16:20:56,151 [INFO] _process_next_query called: is_running=True, current_index=2, pending_count=2
2025-07-11 16:20:56,151 [INFO] All queries processed, finishing
2025-07-11 16:20:57,696 [INFO] 批量查询完成 - 成功: 2, 失败: 0
2025-07-11 16:21:15,021 [INFO] _process_next_query called: is_running=True, current_index=0, pending_count=2
2025-07-11 16:21:15,022 [INFO] Processing query 1/2: 联储证券股份有限公司
2025-07-11 16:21:17,950 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 16:21:19,971 [INFO] ✅ 失信人查询成功: '联储证券股份有限公司' (来源: 缓存)
2025-07-11 16:21:19,971 [INFO] Moving to next query: index now 1/2
2025-07-11 16:21:20,972 [INFO] _process_next_query called: is_running=True, current_index=1, pending_count=2
2025-07-11 16:21:20,972 [INFO] Processing query 2/2: 北京远翰国际教育咨询有限责任公司
2025-07-11 16:21:22,086 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: True
2025-07-11 16:21:22,095 [INFO] 显示结果: '北京远翰国际教育咨询有限责任公司', 缓存状态: True
2025-07-11 16:21:22,104 [INFO] ✅ 失信人查询成功: '北京远翰国际教育咨询有限责任公司' (来源: 缓存)
2025-07-11 16:21:22,105 [INFO] Moving to next query: index now 2/2
2025-07-11 16:21:22,105 [INFO] All queries completed, will finish on next _process_next_query call
2025-07-11 16:21:22,105 [INFO] _process_next_query called: is_running=True, current_index=2, pending_count=2
2025-07-11 16:21:22,105 [INFO] All queries processed, finishing
2025-07-11 16:21:24,518 [INFO] 批量查询完成 - 成功: 2, 失败: 0
2025-07-11 16:21:37,194 [INFO] _process_next_query called: is_running=True, current_index=0, pending_count=2
2025-07-11 16:21:37,195 [INFO] Processing query 1/2: 联储证券股份有限公司
2025-07-11 16:21:47,430 [INFO] 批量查询完成 - 成功: 0, 失败: 0
2025-07-11 16:21:48,689 [INFO] 开始缓存失信人查询结果，查询字符串: '联储证券股份有限公司'
2025-07-11 16:21:48,690 [INFO] 使用失信人缓存键: '联储证券股份有限公司'
2025-07-11 16:21:48,693 [INFO] 已保存到失信人持久化缓存: '联储证券股份有限公司'
2025-07-11 16:21:48,708 [INFO] 已添加到失信人内存缓存: '联储证券股份有限公司'
2025-07-11 16:21:48,708 [INFO] ✅ 失信人查询成功: '联储证券股份有限公司' (来源: API)
2025-07-11 16:21:48,709 [INFO] Moving to next query: index now 1/2
2025-07-11 16:21:49,710 [INFO] _process_next_query called: is_running=True, current_index=1, pending_count=2
2025-07-11 16:21:49,710 [INFO] Processing query 2/2: 北京远翰国际教育咨询有限责任公司
2025-07-11 16:21:52,231 [INFO] 开始缓存失信人查询结果，查询字符串: '北京远翰国际教育咨询有限责任公司'
2025-07-11 16:21:52,231 [INFO] 使用失信人缓存键: '北京远翰国际教育咨询有限责任公司'
2025-07-11 16:21:52,234 [INFO] 已保存到失信人持久化缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-11 16:21:52,245 [INFO] 已添加到失信人内存缓存: '北京远翰国际教育咨询有限责任公司'
2025-07-11 16:21:52,247 [INFO] 显示结果: '联储证券股份有限公司', 缓存状态: False
2025-07-11 16:21:52,256 [INFO] 显示结果: '北京远翰国际教育咨询有限责任公司', 缓存状态: False
2025-07-11 16:21:52,267 [INFO] ✅ 失信人查询成功: '北京远翰国际教育咨询有限责任公司' (来源: API)
2025-07-11 16:21:52,267 [INFO] Moving to next query: index now 2/2
2025-07-11 16:21:52,268 [INFO] All queries completed, will finish on next _process_next_query call
2025-07-11 16:21:52,268 [INFO] _process_next_query called: is_running=True, current_index=2, pending_count=2
2025-07-11 16:21:52,269 [INFO] All queries processed, finishing
2025-07-11 16:21:53,659 [INFO] 批量查询完成 - 成功: 2, 失败: 0
2025-07-11 16:21:55,752 [INFO] 应用程序退出，退出代码：0
