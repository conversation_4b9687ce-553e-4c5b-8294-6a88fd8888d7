#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图标转换工具
将图标文件转换为base64编码，用于嵌入到应用程序中
"""

import base64
import os
import sys


def convert_icon_to_base64(icon_path: str) -> str:
    """
    将图标文件转换为base64编码
    
    Args:
        icon_path (str): 图标文件路径
        
    Returns:
        str: base64编码的字符串
    """
    try:
        with open(icon_path, 'rb') as icon_file:
            icon_data = icon_file.read()
            base64_data = base64.b64encode(icon_data).decode('utf-8')
            return base64_data
    except Exception as e:
        print(f"转换图标文件时出错: {str(e)}")
        return ""


def main():
    """主函数"""
    # 默认图标路径
    default_icon_path = os.path.join('resources', 'icon.ico')
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        icon_path = sys.argv[1]
    else:
        icon_path = default_icon_path
    
    # 检查文件是否存在
    if not os.path.exists(icon_path):
        print(f"错误: 图标文件不存在: {icon_path}")
        print(f"使用方法: python {sys.argv[0]} [图标文件路径]")
        print(f"默认路径: {default_icon_path}")
        sys.exit(1)
    
    # 转换图标
    print(f"正在转换图标文件: {icon_path}")
    base64_data = convert_icon_to_base64(icon_path)
    
    if base64_data:
        print("\n转换成功！请将以下base64数据复制到resource_utils.py中的ICON_BASE64_DATA字典中：")
        print("\n" + "="*80)
        print(f"'main_icon': \"{base64_data}\",")
        print("="*80)
        
        # 保存到文件
        output_file = 'icon_base64.txt'
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"# Base64 encoded icon data for {icon_path}\n")
                f.write(f"# Generated by {sys.argv[0]}\n\n")
                f.write("ICON_BASE64_DATA = {\n")
                f.write(f"    'main_icon': \"{base64_data}\",\n")
                f.write("}\n")
            print(f"\nBase64数据也已保存到文件: {output_file}")
        except Exception as e:
            print(f"保存到文件时出错: {str(e)}")
        
        # 显示文件信息
        file_size = os.path.getsize(icon_path)
        base64_size = len(base64_data)
        print(f"\n文件信息:")
        print(f"  原始文件大小: {file_size} 字节")
        print(f"  Base64编码大小: {base64_size} 字符")
        print(f"  编码效率: {base64_size/file_size:.2f}x")
        
    else:
        print("转换失败！")
        sys.exit(1)


if __name__ == '__main__':
    main()
