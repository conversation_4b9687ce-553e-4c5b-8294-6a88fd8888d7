import sys
import logging
from datetime import datetime
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from login_window import LoginWindow
from resource_utils import get_application_icon

def setup_logging():
    """设置日志记录"""
    log_file = f'app_{datetime.now().strftime("%Y%m%d")}.log'
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def handle_exception(exc_type, exc_value, exc_traceback):
    """处理未捕获的异常"""
    if issubclass(exc_type, KeyboardInterrupt):
        # 对于键盘中断，使用系统默认处理
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    logging.error("未捕获的异常:", exc_info=(exc_type, exc_value, exc_traceback))

def main():
    """主函数"""
    try:
        # 设置日志记录
        setup_logging()
        logging.info("应用程序启动")

        # 设置异常处理
        sys.excepthook = handle_exception

        # 创建应用程序
        app = QApplication(sys.argv)

        # 设置应用程序图标
        app_icon = get_application_icon()
        if not app_icon.isNull():
            app.setWindowIcon(app_icon)
            logging.info("应用程序图标已设置")
        else:
            logging.warning("应用程序图标加载失败")

        # 设置应用程序属性
        app.setStyle('Fusion')  # 使用Fusion样式
        app.setAttribute(Qt.AA_UseHighDpiPixmaps)  # 高DPI支持

        # 创建并显示登录窗口
        login_window = LoginWindow()
        login_window.show()

        # 运行应用程序
        exit_code = app.exec_()
        logging.info(f"应用程序退出，退出代码：{exit_code}")
        sys.exit(exit_code)

    except Exception as e:
        logging.error(f"启动过程中出现错误：{str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == '__main__':
    main()
