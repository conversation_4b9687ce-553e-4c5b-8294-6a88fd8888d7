from PyQt5.QtWidgets import (QLabel, QLineEdit, QPushButton,
                             QVBoxLayout, QHBoxLayout, QMessageBox,
                             QDialog)
from PyQt5.QtCore import Qt
from main_window import MainWindow
from api_client import APIClient
from resource_utils import get_application_icon
import logging

class LoginWindow(QDialog):
    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        # 设置窗口标题和大小
        self.setWindowTitle('联储证券企业信息查询系统')
        self.setFixedSize(400, 150)
        self.setWindowFlags(self.windowFlags() | Qt.CustomizeWindowHint | Qt.MSWindowsFixedSizeDialogHint)

        # 设置窗口图标
        window_icon = get_application_icon()
        if not window_icon.isNull():
            self.setWindowIcon(window_icon)

        # 创建主布局
        layout = QVBoxLayout()
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 添加提示标签
        title_label = QLabel('企业信息查询系统')
        title_label.setStyleSheet('font-size: 16px; font-weight: bold; color: #333;')
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 输入框容器
        input_layout = QHBoxLayout()
        input_layout.setSpacing(10)

        # 标签
        label = QLabel('密钥:')
        label.setStyleSheet('color: #666;')
        input_layout.addWidget(label)

        # 输入框
        self.key_input = QLineEdit()
        self.key_input.setPlaceholderText('请通过审计部门获取密钥')
        self.key_input.setEchoMode(QLineEdit.Password)
        self.key_input.returnPressed.connect(self.confirm_clicked)
        input_layout.addWidget(self.key_input)

        layout.addLayout(input_layout)

        # 按钮布局
        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(10)

        self.confirm_btn = QPushButton('确认')
        self.cancel_btn = QPushButton('取消')

        # 设置按钮大小
        self.confirm_btn.setFixedSize(80, 30)
        self.cancel_btn.setFixedSize(80, 30)

        # 添加按钮事件
        self.confirm_btn.clicked.connect(self.confirm_clicked)
        self.cancel_btn.clicked.connect(self.reject)

        # 添加弹簧
        btn_layout.addStretch()
        btn_layout.addWidget(self.confirm_btn)
        btn_layout.addWidget(self.cancel_btn)

        layout.addLayout(btn_layout)

        # 设置布局
        self.setLayout(layout)

        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background-color: white;
            }
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 13px;
                background-color: #fafafa;
            }
            QLineEdit:focus {
                border-color: #1890ff;
                background-color: white;
            }
            QPushButton {
                padding: 5px 15px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #f5f5f5;
                color: #333;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #e6e6e6;
                border-color: #adadad;
            }
            QPushButton:pressed {
                background-color: #d4d4d4;
            }
            QLabel {
                font-size: 13px;
            }
        """)

        # 设置初始焦点
        self.key_input.setFocus()

    def confirm_clicked(self):
        """确认按钮点击事件"""
        key = self.key_input.text().strip()

        # 验证密钥是否为空
        if not key:
            QMessageBox.warning(self, '提示', '请输入密钥')
            self.key_input.setFocus()
            return

        try:
            # 创建API客户端实例 - 移除预验证，直接进入程序
            api_client = APIClient(key)

            # 显示进度
            self.confirm_btn.setText('启动中...')
            self.confirm_btn.setEnabled(False)

            # 直接创建并显示主窗口，让用户在实际使用时验证
            self.main_window = MainWindow(api_client)
            self.main_window.show()
            self.accept()  # 关闭登录窗口

        except Exception as e:
            logging.error(f"启动失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, '错误', f'程序启动失败: {str(e)}\n请稍后重试')
        finally:
            # 恢复按钮状态
            self.confirm_btn.setText('确认')
            self.confirm_btn.setEnabled(True)
