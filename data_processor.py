from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Callable, Union
from datetime import datetime
import pandas as pd
from openpyxl.utils import get_column_letter
import json
from openpyxl.styles import Alignment, Font, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows
from config_manager import DataDisplayConfig
import logging

class CompanyData:
    """企业数据结构"""
    def __init__(self):
        # 基本信息
        self.entName = ''                # 企业名称
        self.entStatus = ''              # 企业状态
        self.unifiedCode = ''            # 统一社会信用代码
        self.regNo = ''                  # 注册号
        self.legalPerson = ''            # 法定代表人
        self.regCapital = ''             # 注册资本
        self.esDate = ''                 # 成立日期
        self.entType = ''                # 企业类型
        self.scope = ''                  # 经营范围
        self.regAddr = ''                # 注册地址
        self.regOrg = ''                 # 登记机关
        self.cancelDate = ''             # 注销日期
        self.cancelReason = ''           # 注销原因
        self.industry = ''               # 行业
        self.revokeDate = ''             # 吊销日期
        self.revokeReason = ''           # 吊销原因
        self.updateTime = ''             # 更新时间
        self.province = ''               # 省份简称
        self.email = ''                  # 邮箱
        self.website = ''                # 网站
        self.telephone = ''              # 电话
        self.logo = ''                   # 企业logo
        self.property3 = ''              # 企业英文名
        self.fromDate = ''               # 营业期限开始
        self.toDate = ''                 # 营业期限结束

        # 列表数据
        self.abnormalList = []          # 经营异常列表
        self.changeList = []            # 企业变更列表
        self.illegalList = []           # 严重违法列表
        self.staffList = []             # 主要人员列表
        self.licenseList = []           # 行政许可列表
        self.checkList = []             # 抽查检查列表
        self.punishList = []            # 行政处罚列表
        self.branchList = []            # 分支机构列表
        self.reportList = []            # 年报列表

        # 统计数据
        self.branchCount = 0            # 分支机构数量
        self.shareholderCount = 0       # 股东数量
        self.investCount = 0            # 对外投资数量

        # 详细信息对象
        self.briefCancel = None         # 简易注销信息

        # 统计数据
        self.branchCount = 0            # 分支机构数量
        self.shareholderCount = 0       # 股东数量
        self.investCount = 0            # 对外投资数量

class DataProcessor(CompanyData):
    def __init__(self, display_config: 'DataDisplayConfig' = None):
        super().__init__()
        self._progress_callback = None
        self._error_callback = None
        self._total_steps = 0
        self._current_step = 0
        self._excel_title_fill = PatternFill(start_color='CCE5FF', end_color='CCE5FF', fill_type='solid')
        self._excel_title_font = Font(bold=True)
        self._display_config = display_config or DataDisplayConfig()

        # 基本信息字段映射 - 根据实际JSON结构更新
        self._basic_field_map = {
            'name': 'entName',           # JSON中使用name
            'creditCode': 'unifiedCode', # JSON中使用creditCode
            'regNumber': 'regNo',        # JSON中使用regNumber
            'regStatus': 'entStatus',    # JSON中使用regStatus
            'legalPersonName': 'legalPerson',  # JSON中使用legalPersonName
            'regCapital': 'regCapital',
            'estiblishTime': 'esDate',   # JSON中使用estiblishTime
            'companyOrgType': 'entType', # JSON中使用companyOrgType
            'businessScope': 'scope',    # JSON中使用businessScope
            'regLocation': 'regAddr',    # JSON中使用regLocation
            'regInstitute': 'regOrg',    # JSON中使用regInstitute
            'industry': 'industry',
            'property3': 'property3',
            'phoneNumber': 'telephone',  # JSON中使用phoneNumber
            'email': 'email',
            'website': 'website',
            'updateTime': 'updateTime',
            'province': 'province',
            'cancelDate': 'cancelDate',
            'cancelReason': 'cancelReason',
            'revokeDate': 'revokeDate',
            'revokeReason': 'revokeReason',
            'fromTime': 'fromDate',      # JSON中使用fromTime
            'approvedTime': 'toDate'     # JSON中使用approvedTime
        }

        # 列表数据映射
        self._list_field_map = {
            'abnormalList': 'abnormalList',
            'changeList': 'changeList',
            'illegalList': 'illegalList',
            'staffList': 'staffList',
            'licenseList': 'licenseList',
            'checkList': 'checkList',
            'punishList': 'punishList',
            'branchList': 'branchList',
            'reportList': 'reportList'
        }

    def update_config(self, display_config: 'DataDisplayConfig'):
        """更新显示配置"""
        self._display_config = display_config

    def set_callbacks(self, progress_callback: Callable[[str, int, int], None] = None,
                     error_callback: Callable[[str], None] = None):
        """设置进度和错误回调函数"""
        self._progress_callback = progress_callback
        self._error_callback = error_callback

    def _update_progress(self, stage: str, current: int = None, total: int = None):
        """更新进度"""
        if current is None:
            self._current_step += 1
            current = self._current_step
        if total is None:
            total = self._total_steps

        if self._progress_callback:
            self._progress_callback(stage, current, total)

    def _report_error(self, error: str):
        """报告错误"""
        if self._error_callback:
            self._error_callback(error)
        print(f"错误: {error}")

    def _format_worksheet(self, worksheet):
        """格式化工作表"""
        try:
            # 调整列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter

                for cell in column:
                    try:
                        if cell.value:
                            cell_length = len(str(cell.value))
                            max_length = max(max_length, cell_length)
                    except:
                        pass

                # 设置列宽，最小10，最大100
                adjusted_width = min(max(max_length + 2, 10), 100)
                worksheet.column_dimensions[column_letter].width = adjusted_width

            # 设置表头样式
            from openpyxl.styles import Font, Alignment, PatternFill
            header_font = Font(bold=True, size=11)
            header_fill = PatternFill(start_color='CCE5FF', end_color='CCE5FF', fill_type='solid')
            center_aligned = Alignment(horizontal='center', vertical='center')

            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_aligned

            # 冻结首行
            worksheet.freeze_panes = 'A2'

        except Exception as e:
            print(f"格式化工作表时出错: {str(e)}")

    def _field_mapping(self) -> Dict[str, str]:
        """返回API字段到属性的映射关系"""
        return self._basic_field_map.copy()

    def _process_value(self, value: Any) -> str:
        """处理字段值，统一格式化输出"""
        if value is None:
            return ''

        if isinstance(value, (list, dict)):
            try:
                return json.dumps(value, ensure_ascii=False, indent=2)
            except:
                return str(value)

        if isinstance(value, str):
            # 清理HTML
            value = self._clean_html(value)
            # 尝试格式化日期
            if any(char in value for char in '/-年'):
                value = self._format_date(value)
            # 处理空值和特殊值
            if value.lower() in ['无', '暂无', '未知', 'none', 'null', '']:
                return ''

        return str(value).strip()

    def _safe_get_text(self, data: dict, key: str) -> str:
        """安全获取文本数据，处理特殊值"""
        value = data.get(key, '')
        return self._process_value(value)

    def _safe_get_list(self, data: dict, key: str) -> list:
        """安全获取列表数据，确保返回列表类型"""
        value = data.get(key, [])
        if not isinstance(value, list):
            return []
        return value

    def _safe_get_dict(self, data: dict, key: str) -> dict:
        """安全获取字典数据，确保返回字典类型"""
        value = data.get(key, {})
        if not isinstance(value, dict):
            return {}
        return value

    def parse_company_basic_info(self, content: dict):
        """解析企业基本信息

        Returns:
            self: 返回自身以支持链式调用，失败时返回False
        """
        try:
            if not content:
                self._report_error("数据为空")
                return False

            # 获取基本信息
            basic_info = content.get('result', {})
            if not basic_info:
                basic_info = content

            # 使用字段映射更新属性
            for api_field, class_field in self._basic_field_map.items():
                value = self._process_value(basic_info.get(api_field, ''))
                setattr(self, class_field, value)

            # 处理列表数据 - 根据你提供的JSON结构更新字段映射
            self.abnormalList = self._clean_list_data(basic_info.get('abnormalList', []))
            self.changeList = self._clean_list_data(basic_info.get('changeList', []))
            self.illegalList = self._clean_list_data(basic_info.get('illegalList', []))
            self.staffList = self._clean_list_data(basic_info.get('staffList', []))
            self.licenseList = self._clean_list_data(basic_info.get('licenseList', []))
            self.checkList = self._clean_list_data(basic_info.get('checkList', []))
            self.punishList = self._clean_list_data(basic_info.get('punishList', []))
            self.branchList = self._clean_list_data(basic_info.get('branchList', []))
            self.reportList = self._clean_list_data(basic_info.get('reportList', []))

            # 处理简易注销信息
            self.briefCancel = basic_info.get('briefCancel', {})

            # 更新统计数据
            self.branchCount = len(self.branchList)
            self.shareholderCount = len(basic_info.get('shareholderList', []))
            self.investCount = len(basic_info.get('investList', []))

            return self  # 返回自身以支持测试代码
        except Exception as e:
            error_msg = f"解析企业基本信息时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            self._report_error(error_msg)
            return False

    def _clean_list_data(self, data_list: List[Dict]) -> List[Dict]:
        """清理列表数据中的HTML标签和特殊字符"""
        if not isinstance(data_list, list):
            return []

        cleaned_list = []
        for item in data_list:
            if not isinstance(item, dict):
                continue

            cleaned_item = {}
            for key, value in item.items():
                cleaned_item[key] = self._process_value(value)
            cleaned_list.append(cleaned_item)

        return cleaned_list

    def _clean_html(self, text: str) -> str:
        """清理HTML标签和实体，改进文本格式化"""
        if not isinstance(text, str):
            return self._process_value(text)

        # 替换常见HTML实体和标签
        replacements = {
            '&lt;': '<',
            '&gt;': '>',
            '&amp;': '&',
            '&quot;': '"',
            '&nbsp;': ' ',
            '&apos;': "'",
            '<br>': '\n',
            '<br/>': '\n',
            '<br />': '\n',
            '<BR>': '\n',
            '<BR/>': '\n',
            '<BR />': '\n',
            '<p>': '\n',
            '</p>': '\n',
            '<P>': '\n',
            '</P>': '\n',
            '<div>': '\n',
            '</div>': '\n',
            '<DIV>': '\n',
            '</DIV>': '\n',
            '<span>': '',
            '</span>': '',
            '<SPAN>': '',
            '</SPAN>': '',
        }

        result = text
        for old, new in replacements.items():
            result = result.replace(old, new)

        # 移除其他HTML标签（使用正则表达式）
        import re
        result = re.sub(r'<[^>]+>', '', result)

        # 处理多个连续的换行符
        result = re.sub(r'\n\s*\n', '\n', result)

        # 移除行首行尾空格，但保留换行
        lines = result.split('\n')
        cleaned_lines = [line.strip() for line in lines if line.strip()]

        # 对于长文本，添加适当的格式化
        if len(cleaned_lines) > 1:
            # 如果有多行，保持换行结构
            result = '\n'.join(cleaned_lines)
        else:
            # 单行文本，直接返回
            result = ' '.join(cleaned_lines)

        return result or ''

    def validate_data(self) -> bool:
        """验证数据完整性"""
        required_fields = ['name', 'credit_code', 'reg_status']
        missing_fields = []

        for field in required_fields:
            if not getattr(self, field, None):
                missing_fields.append(field)

        if missing_fields:
            self._report_error(f"缺少必要字段: {', '.join(missing_fields)}")
            return False

        return True

    def _format_date(self, date_str: str) -> str:
        """格式化日期字符串"""
        if not date_str:
            return ''

        try:
            # 处理常见的日期格式
            formats = [
                '%Y-%m-%d',
                '%Y/%m/%d',
                '%Y年%m月%d日',
                '%Y-%m-%d %H:%M:%S',
                '%Y/%m/%d %H:%M:%S',
                '%Y年%m月%d日 %H:%M:%S'
            ]

            for fmt in formats:
                try:
                    date_obj = datetime.strptime(date_str, fmt)
                    # 如果有时间部分且不是午夜，则包含时间
                    if date_obj.hour == 0 and date_obj.minute == 0 and date_obj.second == 0:
                        return date_obj.strftime('%Y-%m-%d')
                    return date_obj.strftime('%Y-%m-%d %H:%M:%S')
                except ValueError:
                    continue

            return date_str
        except Exception as e:
            logging.debug(f"日期格式化失败: {str(e)}")
            return date_str

    def _clean_html_list(self, data_list: List[Dict]) -> List[Dict]:
        """清理列表中的HTML内容"""
        if not isinstance(data_list, list):
            return []
        return [{k: self._clean_html(v) if isinstance(v, str) else v
                for k, v in item.items()}
                for item in data_list]

    def format_result_for_display(self, data: dict) -> dict:
        """格式化数据用于显示"""
        try:
            if not data:
                return {}

            result = {}

            # 使用字段映射将API数据转换为属性
            field_map = self._field_mapping()
            basic_info = {}
            for api_key, attr_name in field_map.items():
                value = self._safe_get_text(data, api_key)
                if value:
                    # 将下划线命名转换为显示名称
                    display_name = ' '.join(word.capitalize() for word in attr_name.split('_'))
                    basic_info[display_name] = value

            result['basic_info'] = basic_info

            # 处理其他可选部分
            optional_sections = [
                ('branch_info', self._display_config.show_branch_info, 'branchList'),
                ('annual_reports', self._display_config.show_annual_reports, 'reportList'),
                ('judicial_assistance', self._display_config.show_judicial_assistance, 'judicialList'),
                ('mortgage_info', self._display_config.show_mortgage_info, 'mortList'),
                ('ipr_pledge', self._display_config.show_ipr_pledge, 'iprPledgeList')
            ]

            for section_name, is_enabled, list_key in optional_sections:
                if is_enabled:
                    items = data.get(list_key, [])
                    if items:
                        result[section_name] = {
                            'items': self._format_list_items(items)
                        }

            return result
        except Exception as e:
            logging.error(f"格式化显示数据时出错: {str(e)}", exc_info=True)
            return {'error': str(e)}

    def _format_section(self, data: dict, section_config: dict) -> dict:
        """格式化单个部分的数据"""
        result = {}

        # 获取部分标题
        result['title'] = section_config.get('title', '')

        # 如果有列表键，则处理列表数据
        list_key = section_config.get('list_key')
        if list_key:
            items = data.get(list_key, [])
            if not isinstance(items, list):
                items = []
            result['items'] = []

            for item in items:
                formatted_item = {}
                for field in section_config.get('fields', []):
                    key = field['key']
                    display = field['display']
                    mapping = field.get('mapping', {})

                    value = item.get(key, '')
                    if mapping and str(value) in mapping:
                        value = mapping[str(value)]

                    formatted_item[display] = value
                result['items'].append(formatted_item)
        else:
            # 处理单个对象数据
            for field in section_config.get('fields', []):
                key = field['key']
                display = field['display']
                mapping = field.get('mapping', {})

                value = data.get(key, '')
                if mapping and str(value) in mapping:
                    value = mapping[str(value)]

                result[display] = value

        return result

    def _format_field_for_display(self, field_name: str, value: Any) -> str:
        """格式化字段用于显示"""
        try:
            if value is None or value == '':
                return '未提供'
            if value in ['无', '暂无', '未知']:
                return value
            if isinstance(value, (list, dict)):
                return json.dumps(value, ensure_ascii=False, indent=2)
            return str(value)
        except Exception as e:
            logging.error(f"格式化字段 {field_name} 时出错: {str(e)}")
            return '格式化错误'

    def _process_nested_list(self, data_list: List[Dict], field_names: Dict[str, str]) -> pd.DataFrame:
        """处理嵌套的列表数据
        Args:
            data_list: 要处理的数据列表
            field_names: 字段名称映射字典 {'api_field': 'display_name'}
        """
        if not data_list:
            return pd.DataFrame()

        processed_data = []
        for item in data_list:
            row_data = {}
            for api_field, display_name in field_names.items():
                value = item.get(api_field, '')
                # 处理嵌套的字典或列表
                if isinstance(value, (dict, list)):
                    try:
                        value = json.dumps(value, ensure_ascii=False)
                    except:
                        value = str(value)
                row_data[display_name] = value
            processed_data.append(row_data)

        return pd.DataFrame(processed_data)

    def _safe_get(self, data: Union[Dict, None], key: str, default: Any = '') -> Any:
        """安全获取字典值

        Args:
            data: 字典数据
            key: 键名
            default: 默认值

        Returns:
            获取到的值或默认值
        """
        if data is None or not isinstance(data, dict):
            return default
        value = data.get(key, default)
        return value if value is not None else default

    def _get_attribute_name(self, api_key: str) -> str:
        """将API字段名转换为属性名
        例如：entName -> name, unifiedCode -> credit_code
        """
        mapping = {
            'entName': 'name',
            'unifiedCode': 'credit_code',
            'regNo': 'reg_number',
            'entStatus': 'reg_status',
            'legalPerson': 'legal_person',
            'regCapital': 'reg_capital',
            'esDate': 'establish_time',
            'entType': 'company_org_type',
            'regAddr': 'reg_location',
            'regOrg': 'reg_institute',
            'property3': 'english_name',
            'telephone': 'phone_number'
        }
        return mapping.get(api_key, api_key)

    def _get_api_name(self, attr_name: str) -> str:
        """将属性名转换为API字段名
        例如：name -> entName, credit_code -> unifiedCode
        """
        mapping = {
            'name': 'entName',
            'credit_code': 'unifiedCode',
            'reg_number': 'regNo',
            'reg_status': 'entStatus',
            'legal_person': 'legalPerson',
            'reg_capital': 'regCapital',
            'establish_time': 'esDate',
            'company_org_type': 'entType',
            'reg_location': 'regAddr',
            'reg_institute': 'regOrg',
            'english_name': 'property3',
            'phone_number': 'telephone'
        }
        return mapping.get(attr_name, attr_name)

    def export_to_excel(self, data_or_filename: Union[dict, str], filename: str = None) -> bool:
        """导出数据到Excel文件

        Args:
            data_or_filename: 如果是字符串，则为文件名；如果是字典，则为数据
            filename: 当第一个参数是数据时，这是文件名
        """
        try:
            # 处理参数
            if isinstance(data_or_filename, str) and filename is None:
                # 旧的调用方式：export_to_excel(filename)
                filename = data_or_filename
                data = None
            elif isinstance(data_or_filename, dict):
                # 新的调用方式：export_to_excel(data, filename)
                data = data_or_filename
                if filename is None:
                    self._report_error("缺少文件名参数")
                    return False
            else:
                self._report_error("参数类型错误")
                return False

            # 如果传入了数据，先解析数据
            if data:
                if not self.parse_company_basic_info(data):
                    self._report_error("数据解析失败")
                    return False

            # 检查是否有数据可导出 - 放宽条件，只要有任何基本信息即可
            has_basic_data = any([
                getattr(self, 'entName', ''),
                getattr(self, 'unifiedCode', ''),
                getattr(self, 'regNo', ''),
                getattr(self, 'entStatus', '')
            ])

            if not has_basic_data:
                self._report_error("数据为空或无效")
                return False

            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 基本信息表
                basic_info = {
                    '企业名称': [self._process_value(self.entName)],
                    '英文名': [self._process_value(self.property3)],
                    '统一社会信用代码': [self._process_value(self.unifiedCode)],
                    '注册号': [self._process_value(self.regNo)],
                    '企业状态': [self._process_value(self.entStatus)],
                    '法定代表人': [self._process_value(self.legalPerson)],
                    '注册资本': [self._process_value(self.regCapital)],
                    '成立日期': [self._process_value(self.esDate)],
                    '企业类型': [self._process_value(self.entType)],
                    '所属行业': [self._process_value(self.industry)],
                    '经营范围': [self._process_value(self.scope)],
                    '注册地址': [self._process_value(self.regAddr)],
                    '登记机关': [self._process_value(self.regOrg)],
                    '电话': [self._process_value(self.telephone)],
                    '邮箱': [self._process_value(self.email)],
                    '网站': [self._process_value(self.website)],
                    '营业期限开始': [self._process_value(self.fromDate)],
                    '营业期限结束': [self._process_value(self.toDate)],
                    '更新时间': [self._process_value(self.updateTime)]
                }

                # 添加注销或吊销相关信息
                if self.entStatus == "已注销":
                    basic_info.update({
                        '注销日期': [self._process_value(self.cancelDate)],
                        '注销原因': [self._process_value(self.cancelReason)]
                    })
                elif self.entStatus == "已吊销":
                    basic_info.update({
                        '吊销日期': [self._process_value(self.revokeDate)],
                        '吊销原因': [self._process_value(self.revokeReason)]
                    })

                # 写入基本信息表
                basic_df = pd.DataFrame(basic_info)
                basic_df.to_excel(writer, sheet_name='基本信息', index=False)

                # 处理列表数据 - 根据显示配置决定是否导出
                # 定义所有可能的列表数据映射
                all_list_data = {
                    'abnormal': (self.abnormalList, '经营异常', [
                        'putDate', 'putReason', 'putDepartment',
                        'removeDate', 'removeReason', 'removeDepartment', 'id'
                    ], [
                        '列入日期', '列入原因', '决定列入机关',
                        '移出日期', '移出原因', '移出机关', 'ID'
                    ]),
                    'change': (self.changeList, '变更信息', [
                        'changeTime', 'changeItem', 'contentBefore', 'contentAfter', 'id'
                    ], [
                        '变更时间', '变更事项', '变更前', '变更后', 'ID'
                    ]),
                    'illegal': (self.illegalList, '严重违法', [
                        'putDate', 'putReason', 'putDepartment', 'fact', 'type', 'industry',
                        'removeDate', 'removeReason', 'removeDepartment', 'revokeDate', 'id'
                    ], [
                        '列入日期', '列入原因', '决定列入机关', '违法事实', '类别', '行业',
                        '移出日期', '移出原因', '决定移出机关', '吊销日期', 'ID'
                    ]),
                    'staff': (self.staffList, '主要人员', [
                        'name', 'staffTypeName', 'type', 'legalPersonName', 'legalPersonType',
                        'regNumber', 'creditCode', 'property3', 'revokeReason', 'id'
                    ], [
                        '姓名', '职位', '类型', '法人', '法人类型',
                        '注册号', '统一社会信用代码', '英文名', '吊销原因', 'ID'
                    ]),
                    'license': (self.licenseList, '行政许可', [
                        'licencenumber', 'licencename', 'department', 'scope',
                        'fromdate', 'todate', 'source', 'updatetime', 'base', 'id'
                    ], [
                        '许可书文编号', '许可证名称', '发证机关', '范围',
                        '起始日期', '截止日期', '来源', '更新时间', '省份简称', 'ID'
                    ]),
                    'check': (self.checkList, '抽查检查', [
                        'checkType', 'checkDate', 'checkOrg',
                        'checkResult', 'remark', 'id'
                    ], [
                        '检查类型', '检查日期', '检查实施机关',
                        '检查结果', '备注', 'ID'
                    ]),
                    'punish': (self.punishList, '行政处罚', [
                        'departmentName', 'publishDate', 'type', 'content', 'legalPersonName',
                        'decisionDate', 'regNumber', 'punishNumber', 'name', 'sourceName', 'base', 'id'
                    ], [
                        '作出行政处罚决定机关名称', '发布时间', '违法行为类型', '行政处罚内容', '法定代表人姓名',
                        '作出行政处罚决定日期', '注册号', '行政处罚决定书文号', '公司名称', '来源名称', '省份简称', 'ID'
                    ]),
                    'branch_info': (self.branchList, '分支机构', [
                        'entName', 'regNo', 'entStatus'
                    ], [
                        '企业名称', '注册号', '企业状态'
                    ])
                }

                # Excel导出所有数据，不受显示配置限制
                for section_name, (data_list, sheet_name, api_fields, display_fields) in all_list_data.items():
                    if data_list:
                        # 创建字段映射字典
                        field_map = dict(zip(api_fields, display_fields))

                        # 准备数据
                        rows = []
                        for item in data_list:
                            row = {}
                            for api_field, display_field in field_map.items():
                                value = item.get(api_field, '')
                                # 特殊处理某些字段
                                if api_field == 'typeJoin' and isinstance(value, list):
                                    value = ', '.join(str(v) for v in value)
                                row[display_field] = self._process_value(value)
                            rows.append(row)

                        # 创建DataFrame并写入Excel
                        if rows:
                            df = pd.DataFrame(rows)
                            df.to_excel(writer, sheet_name=sheet_name, index=False)
                            self._format_worksheet(writer.sheets[sheet_name])

                # 处理简易注销信息 - Excel导出所有数据
                if self.briefCancel and isinstance(self.briefCancel, dict):
                    brief_cancel_data = {
                        '登记机关': [self._process_value(self.briefCancel.get('reg_authority', ''))]
                    }

                    # 处理异议列表
                    objection_list = self.briefCancel.get('objectionList', [])
                    if objection_list:
                        objection_rows = []
                        for objection in objection_list:
                            objection_row = {
                                '异议内容': self._process_value(objection.get('objection_content', '')),
                                '异议时间': self._process_value(objection.get('objection_date', '')),
                                '异议申请人': self._process_value(objection.get('objection_apply_person', '')),
                                '公告申请日期': self._process_value(objection.get('announcement_apply_date', '')),
                                '注册号': self._process_value(objection.get('reg_num', '')),
                                '公司名': self._process_value(objection.get('company_name', '')),
                                '统一社会信用代码': self._process_value(objection.get('credit_code', '')),
                                '简易注销结果': self._process_value(objection.get('brief_cancel_result', '')),
                                '公告期': self._process_value(objection.get('announcement_term', '')),
                                '公告结束日期': self._process_value(objection.get('announcement_end_date', ''))
                            }
                            objection_rows.append(objection_row)

                        if objection_rows:
                            objection_df = pd.DataFrame(objection_rows)
                            objection_df.to_excel(writer, sheet_name='简易注销异议', index=False)
                            self._format_worksheet(writer.sheets['简易注销异议'])

                    # 写入简易注销基本信息
                    brief_df = pd.DataFrame(brief_cancel_data)
                    brief_df.to_excel(writer, sheet_name='简易注销', index=False)
                    self._format_worksheet(writer.sheets['简易注销'])

                # 格式化基本信息工作表
                self._format_worksheet(writer.sheets['基本信息'])

            self._update_progress('完成Excel导出', 100, 100)
            return True

        except Exception as e:
            error_msg = f"导出Excel时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            self._report_error(error_msg)
            return False

    def _parse_list_data(self, content: dict) -> None:
        """解析列表数据"""
        if not content or 'result' not in content:
            return

        result = content['result']

        # 解析各类列表数据
        for api_field, class_field in self._list_field_map.items():
            if api_field in result:
                setattr(self, class_field, result[api_field] or [])

        # 设置统计数据
        self.branchCount = len(self.branchList)

        # 解析简易注销信息
        if 'briefCancel' in result:
            self.briefCancel = result['briefCancel']

    def parse_company_info(self, content: dict) -> bool:
        """解析公司信息"""
        try:
            if not content or 'result' not in content:
                self._report_error("数据为空或无效")
                return False

            result = content['result']

            # 解析基本信息
            for api_field, class_field in self._field_mapping().items():
                if api_field in result:
                    setattr(self, class_field, result[api_field] or '')

            # 解析列表数据
            self._parse_list_data(content)

            return True

        except Exception as e:
            self._report_error(f"解析数据时出错: {str(e)}")
            return False
