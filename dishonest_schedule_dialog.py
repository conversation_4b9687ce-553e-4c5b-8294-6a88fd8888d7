"""失信被执行人定时检查设置对话框"""
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                           QLineEdit, QPushButton, QComboBox, QTextEdit,
                           QCheckBox, QSpinBox, QTimeEdit, QDateEdit,
                           QTableWidget, QTableWidgetItem, QMessageBox,
                           QGroupBox, QGridLayout, QHeaderView, QTabWidget,
                           QWidget, QScrollArea)
from PyQt5.QtCore import Qt, QTime, QDate, pyqtSignal
from PyQt5.QtGui import QFont
from dishonest_scheduler_manager import DishonestSchedulerManager, DishonestScheduleTask
from datetime import datetime, timedelta
import json


class DishonestScheduleDialog(QDialog):
    """失信人定时检查设置对话框"""

    def __init__(self, scheduler_manager: DishonestSchedulerManager, parent=None):
        super().__init__(parent)
        self.scheduler_manager = scheduler_manager
        self.init_ui()
        self.load_tasks()

    def init_ui(self):
        self.setWindowTitle('失信被执行人定时检查设置')
        self.setGeometry(200, 200, 800, 600)

        # 创建主布局
        main_layout = QVBoxLayout()

        # 创建选项卡
        tab_widget = QTabWidget()

        # 任务管理选项卡
        task_tab = QWidget()
        self.init_task_tab(task_tab)
        tab_widget.addTab(task_tab, '任务管理')

        # 执行历史选项卡
        history_tab = QWidget()
        self.init_history_tab(history_tab)
        tab_widget.addTab(history_tab, '执行历史')

        main_layout.addWidget(tab_widget)

        # 底部按钮
        button_layout = QHBoxLayout()

        close_btn = QPushButton('关闭')
        close_btn.clicked.connect(self.accept)

        button_layout.addStretch()
        button_layout.addWidget(close_btn)

        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)

    def init_task_tab(self, tab_widget):
        """初始化任务管理选项卡"""
        layout = QVBoxLayout()

        # 新建任务区域
        task_group = QGroupBox("新建/编辑任务")
        task_layout = QGridLayout()

        # 关键词输入
        task_layout.addWidget(QLabel("查询关键词:"), 0, 0)
        self.keywords_input = QTextEdit()
        self.keywords_input.setPlaceholderText("请输入要查询的企业名称或统一社会信用代码（每行一个）")
        self.keywords_input.setMaximumHeight(80)
        task_layout.addWidget(self.keywords_input, 0, 1, 1, 2)

        # 执行时间
        task_layout.addWidget(QLabel("执行时间:"), 1, 0)
        self.time_edit = QTimeEdit()
        self.time_edit.setTime(QTime(9, 0))  # 默认上午9点
        task_layout.addWidget(self.time_edit, 1, 1)

        # 执行频率
        task_layout.addWidget(QLabel("执行频率:"), 1, 2)
        self.frequency_combo = QComboBox()
        self.frequency_combo.addItems(['一次性', '每天', '每周', '每月'])
        self.frequency_combo.currentTextChanged.connect(self.on_frequency_changed)
        task_layout.addWidget(self.frequency_combo, 1, 3)

        # 一次性任务的日期选择
        self.date_label = QLabel("执行日期:")
        task_layout.addWidget(self.date_label, 2, 0)
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate().addDays(1))
        task_layout.addWidget(self.date_edit, 2, 1)

        # 每周任务的星期选择
        self.week_label = QLabel("执行星期:")
        task_layout.addWidget(self.week_label, 2, 2)
        self.week_checkboxes = []
        week_layout = QHBoxLayout()
        week_days = ['一', '二', '三', '四', '五', '六', '日']
        for i, day in enumerate(week_days):
            checkbox = QCheckBox(day)
            self.week_checkboxes.append(checkbox)
            week_layout.addWidget(checkbox)
        week_widget = QWidget()
        week_widget.setLayout(week_layout)
        task_layout.addWidget(week_widget, 2, 3)

        # 每月任务的日期选择
        self.month_label = QLabel("每月日期:")
        task_layout.addWidget(self.month_label, 3, 0)
        self.month_day_spin = QSpinBox()
        self.month_day_spin.setRange(1, 31)
        self.month_day_spin.setValue(1)
        task_layout.addWidget(self.month_day_spin, 3, 1)

        # 启用状态
        self.enabled_checkbox = QCheckBox("启用任务")
        self.enabled_checkbox.setChecked(True)
        task_layout.addWidget(self.enabled_checkbox, 3, 2)

        # 按钮
        button_layout = QHBoxLayout()
        add_btn = QPushButton('添加任务')
        add_btn.clicked.connect(self.add_task)
        update_btn = QPushButton('更新任务')
        update_btn.clicked.connect(self.update_task)
        self.update_btn = update_btn
        self.update_btn.setEnabled(False)

        button_layout.addWidget(add_btn)
        button_layout.addWidget(update_btn)
        button_layout.addStretch()

        task_layout.addLayout(button_layout, 4, 0, 1, 4)
        task_group.setLayout(task_layout)
        layout.addWidget(task_group)

        # 任务列表
        list_group = QGroupBox("现有任务")
        list_layout = QVBoxLayout()

        self.task_table = QTableWidget()
        self.task_table.setColumnCount(6)
        self.task_table.setHorizontalHeaderLabels(['关键词', '执行时间', '频率', '状态', '下次执行', '操作'])
        self.task_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.task_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.task_table.itemSelectionChanged.connect(self.on_task_selected)

        list_layout.addWidget(self.task_table)
        list_group.setLayout(list_layout)
        layout.addWidget(list_group)

        tab_widget.setLayout(layout)

        # 初始化显示状态
        self.on_frequency_changed('一次性')

    def init_history_tab(self, tab_widget):
        """初始化执行历史选项卡"""
        layout = QVBoxLayout()

        # 控制按钮
        control_layout = QHBoxLayout()
        refresh_btn = QPushButton('刷新历史')
        refresh_btn.clicked.connect(self.load_history)
        clear_btn = QPushButton('清除历史')
        clear_btn.clicked.connect(self.clear_history)

        control_layout.addWidget(refresh_btn)
        control_layout.addWidget(clear_btn)
        control_layout.addStretch()

        layout.addLayout(control_layout)

        # 历史记录显示
        self.history_text = QTextEdit()
        self.history_text.setReadOnly(True)
        self.history_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.history_text)

        tab_widget.setLayout(layout)

    def on_frequency_changed(self, frequency):
        """频率改变时的处理"""
        # 隐藏所有特定选项
        self.date_label.setVisible(False)
        self.date_edit.setVisible(False)
        self.week_label.setVisible(False)
        for checkbox in self.week_checkboxes:
            checkbox.parent().setVisible(False)
        self.month_label.setVisible(False)
        self.month_day_spin.setVisible(False)

        # 根据频率显示相应选项
        if frequency == '一次性':
            self.date_label.setVisible(True)
            self.date_edit.setVisible(True)
        elif frequency == '每周':
            self.week_label.setVisible(True)
            for checkbox in self.week_checkboxes:
                checkbox.parent().setVisible(True)
        elif frequency == '每月':
            self.month_label.setVisible(True)
            self.month_day_spin.setVisible(True)

    def add_task(self):
        """添加新任务"""
        try:
            task = self.create_task_from_input()
            if task:
                self.scheduler_manager.add_task(task)
                self.load_tasks()
                self.clear_input()
                QMessageBox.information(self, '成功', '任务添加成功！')
        except Exception as e:
            QMessageBox.critical(self, '错误', f'添加任务失败: {str(e)}')

    def update_task(self):
        """更新选中的任务"""
        try:
            current_row = self.task_table.currentRow()
            if current_row >= 0:
                task = self.create_task_from_input()
                if task:
                    self.scheduler_manager.update_task(current_row, task)
                    self.load_tasks()
                    self.clear_input()
                    self.update_btn.setEnabled(False)
                    QMessageBox.information(self, '成功', '任务更新成功！')
        except Exception as e:
            QMessageBox.critical(self, '错误', f'更新任务失败: {str(e)}')

    def create_task_from_input(self) -> DishonestScheduleTask:
        """从输入创建任务对象"""
        # 获取关键词
        keywords_text = self.keywords_input.toPlainText().strip()
        if not keywords_text:
            raise ValueError("请输入查询关键词")

        keywords = [kw.strip() for kw in keywords_text.split('\n') if kw.strip()]
        if not keywords:
            raise ValueError("请输入有效的查询关键词")

        # 获取执行时间
        time = self.time_edit.time()
        schedule_time = time.toString("HH:mm")

        # 获取频率
        frequency_map = {'一次性': 'once', '每天': 'daily', '每周': 'weekly', '每月': 'monthly'}
        frequency = frequency_map[self.frequency_combo.currentText()]

        # 创建任务
        task = DishonestScheduleTask(
            keywords=keywords,
            schedule_time=schedule_time,
            frequency=frequency,
            enabled=self.enabled_checkbox.isChecked()
        )

        # 设置特定频率的参数
        if frequency == 'once':
            date = self.date_edit.date()
            task.target_date = date.toString("yyyy-MM-dd")
        elif frequency == 'weekly':
            days_of_week = []
            for i, checkbox in enumerate(self.week_checkboxes):
                if checkbox.isChecked():
                    days_of_week.append(i)
            if not days_of_week:
                raise ValueError("请选择至少一个执行日期")
            task.days_of_week = days_of_week
        elif frequency == 'monthly':
            task.day_of_month = self.month_day_spin.value()

        return task

    def clear_input(self):
        """清空输入"""
        self.keywords_input.clear()
        self.time_edit.setTime(QTime(9, 0))
        self.frequency_combo.setCurrentIndex(0)
        self.date_edit.setDate(QDate.currentDate().addDays(1))
        for checkbox in self.week_checkboxes:
            checkbox.setChecked(False)
        self.month_day_spin.setValue(1)
        self.enabled_checkbox.setChecked(True)

    def load_tasks(self):
        """加载任务列表"""
        self.task_table.setRowCount(len(self.scheduler_manager.tasks))

        for i, task in enumerate(self.scheduler_manager.tasks):
            # 关键词
            keywords_text = ', '.join(task.keywords[:3])  # 只显示前3个
            if len(task.keywords) > 3:
                keywords_text += f' 等{len(task.keywords)}个'
            self.task_table.setItem(i, 0, QTableWidgetItem(keywords_text))

            # 执行时间
            self.task_table.setItem(i, 1, QTableWidgetItem(task.schedule_time))

            # 频率
            frequency_map = {'once': '一次性', 'daily': '每天', 'weekly': '每周', 'monthly': '每月'}
            frequency_text = frequency_map.get(task.frequency, task.frequency)
            self.task_table.setItem(i, 2, QTableWidgetItem(frequency_text))

            # 状态
            status_text = "启用" if task.enabled else "禁用"
            task_status = self.scheduler_manager.get_task_status(task)
            if task_status['status'] != '等待中':
                status_text += f" ({task_status['status']})"
            self.task_table.setItem(i, 3, QTableWidgetItem(status_text))

            # 下次执行
            next_run = task.next_run if task.enabled else "已禁用"
            self.task_table.setItem(i, 4, QTableWidgetItem(next_run))

            # 操作按钮
            delete_btn = QPushButton('删除')
            delete_btn.clicked.connect(lambda checked, row=i: self.delete_task(row))
            self.task_table.setCellWidget(i, 5, delete_btn)

    def on_task_selected(self):
        """任务选中时的处理"""
        current_row = self.task_table.currentRow()
        if current_row >= 0 and current_row < len(self.scheduler_manager.tasks):
            task = self.scheduler_manager.tasks[current_row]
            self.load_task_to_input(task)
            self.update_btn.setEnabled(True)
        else:
            self.update_btn.setEnabled(False)

    def load_task_to_input(self, task: DishonestScheduleTask):
        """将任务加载到输入控件"""
        # 关键词
        self.keywords_input.setPlainText('\n'.join(task.keywords))

        # 执行时间
        time = QTime.fromString(task.schedule_time, "HH:mm")
        self.time_edit.setTime(time)

        # 频率
        frequency_map = {'once': '一次性', 'daily': '每天', 'weekly': '每周', 'monthly': '每月'}
        frequency_text = frequency_map.get(task.frequency, '一次性')
        self.frequency_combo.setCurrentText(frequency_text)

        # 特定频率参数
        if task.frequency == 'once' and task.target_date:
            date = QDate.fromString(task.target_date, "yyyy-MM-dd")
            self.date_edit.setDate(date)
        elif task.frequency == 'weekly' and task.days_of_week:
            for i, checkbox in enumerate(self.week_checkboxes):
                checkbox.setChecked(i in task.days_of_week)
        elif task.frequency == 'monthly' and task.day_of_month:
            self.month_day_spin.setValue(task.day_of_month)

        # 启用状态
        self.enabled_checkbox.setChecked(task.enabled)

    def delete_task(self, row):
        """删除任务"""
        reply = QMessageBox.question(self, '确认删除', '确定要删除这个任务吗？',
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.scheduler_manager.remove_task(row)
            self.load_tasks()
            self.clear_input()
            self.update_btn.setEnabled(False)

    def load_history(self):
        """加载执行历史"""
        history_text = "=== 失信被执行人定时检查执行历史 ===\n\n"

        all_results = self.scheduler_manager.check_results
        if not all_results:
            history_text += "暂无执行历史\n"
        else:
            for task_id, records in all_results.items():
                history_text += f"任务ID: {task_id}\n"
                history_text += "-" * 50 + "\n"

                for record in records[-10:]:  # 只显示最近10次
                    execution_time = record.get('execution_time', '')
                    status = record.get('status', '')
                    keywords = record.get('task_info', {}).get('keywords', [])
                    changes_summary = record.get('changes_summary', [])
                    results = record.get('results', {})

                    history_text += f"执行时间: {execution_time}\n"
                    history_text += f"查询关键词: {', '.join(keywords)}\n"
                    history_text += f"执行状态: {status}\n"

                    # 显示详细的查询结果分析
                    if results:
                        history_text += "查询结果详情:\n"
                        for keyword, result_info in results.items():
                            analysis = result_info.get('analysis', {})
                            if analysis:
                                has_records = analysis.get('has_records', False)
                                total_count = analysis.get('total_count', 0)
                                latest_time = analysis.get('latest_publish_time_formatted', '')

                                if has_records:
                                    history_text += f"  {keyword}: 存在失信记录 (共{total_count}条)"
                                    if latest_time:
                                        history_text += f", 最新发布时间: {latest_time}"
                                    history_text += "\n"
                                else:
                                    history_text += f"  {keyword}: 无失信记录\n"
                            else:
                                history_text += f"  {keyword}: 查询结果未知\n"

                    if changes_summary:
                        history_text += "发现变更:\n"
                        for change in changes_summary:
                            history_text += f"  - {change}\n"
                    else:
                        history_text += "未发现变更\n"

                    history_text += "\n"

                history_text += "=" * 60 + "\n\n"

        self.history_text.setText(history_text)

    def clear_history(self):
        """清除执行历史"""
        reply = QMessageBox.question(self, '确认清除', '确定要清除所有执行历史吗？',
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            if self.scheduler_manager.clear_all_check_history():
                QMessageBox.information(self, '成功', '执行历史已清除')
                self.load_history()
            else:
                QMessageBox.critical(self, '错误', '清除执行历史失败')
