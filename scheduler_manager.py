"""定时检查管理模块"""
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import os
import calendar
from typing import Dict, List, Optional
import threading
import time

@dataclass
class ScheduleTask:
    """定时任务"""
    keywords: List[str]      # 要查询的关键词列表
    schedule_time: str       # 计划执行时间 HH:MM 格式
    frequency: str           # 执行频率：once(一次), daily(每天), weekly(每周), monthly(每月)
    enabled: bool = True     # 是否启用
    last_run: str = ""      # 上次执行时间
    target_date: str = ""   # 目标执行日期 YYYY-MM-DD格式（用于一次性任务）
    days_of_week: List[int] = None  # 每周执行的星期几 [0-6]（用于每周任务）
    day_of_month: int = None        # 每月执行的日期 1-31（用于每月任务）
    next_run: str = ""      # 下次执行时间 YYYY-MM-DD HH:MM格式

    def to_dict(self) -> Dict:
        return {
            'keywords': self.keywords,
            'schedule_time': self.schedule_time,
            'frequency': self.frequency,
            'enabled': self.enabled,
            'last_run': self.last_run,
            'target_date': self.target_date,
            'days_of_week': self.days_of_week,
            'day_of_month': self.day_of_month,
            'next_run': self.next_run
        }

    @classmethod
    def from_dict(cls, data: Dict) -> 'ScheduleTask':
        return cls(
            keywords=data.get('keywords', []),
            schedule_time=data.get('schedule_time', ''),
            frequency=data.get('frequency', 'once'),
            enabled=data.get('enabled', True),
            last_run=data.get('last_run', ''),
            target_date=data.get('target_date', ''),
            days_of_week=data.get('days_of_week', None),
            day_of_month=data.get('day_of_month', None),
            next_run=data.get('next_run', '')
        )

    def calculate_next_run(self) -> str:
        """计算下次执行时间"""
        current_time = datetime.now()
        time_parts = self.schedule_time.split(':')
        hour, minute = int(time_parts[0]), int(time_parts[1])

        if self.frequency == 'once':
            if not self.target_date:
                return ''
            target = datetime.strptime(f"{self.target_date} {self.schedule_time}", "%Y-%m-%d %H:%M")
            if target > current_time:
                return target.strftime("%Y-%m-%d %H:%M")
            return ''

        elif self.frequency == 'daily':
            next_run = current_time.replace(hour=hour, minute=minute, second=0, microsecond=0)
            if next_run <= current_time:
                next_run = next_run + timedelta(days=1)

        elif self.frequency == 'weekly':
            if not self.days_of_week:
                return ''
            next_run = current_time.replace(hour=hour, minute=minute, second=0, microsecond=0)
            while next_run.weekday() not in self.days_of_week or next_run <= current_time:
                next_run = next_run + timedelta(days=1)

        elif self.frequency == 'monthly':
            if not self.day_of_month or self.day_of_month < 1 or self.day_of_month > 31:
                return ''
            next_run = current_time.replace(day=self.day_of_month, hour=hour, minute=minute,
                                          second=0, microsecond=0)
            if next_run <= current_time:
                # 移至下个月
                if current_time.month == 12:
                    next_run = next_run.replace(year=current_time.year + 1, month=1)
                else:
                    next_run = next_run.replace(month=current_time.month + 1)

        else:
            return ''

        return next_run.strftime("%Y-%m-%d %H:%M")

@dataclass
class CompanyChange:
    """企业变更信息"""
    field_name: str      # 变更字段
    old_value: str      # 原值
    new_value: str      # 新值
    change_time: str    # 变更时间

class SchedulerManager:
    """调度管理器"""
    def __init__(self, api_client=None):
        self.api_client = api_client
        self.tasks: List[ScheduleTask] = []
        self.running = False
        self.thread = None
        self.task_status = {}  # 存储任务执行状态
        self._status_lock = threading.Lock()
        self.config_file = "schedule_config.json"
        self.history_file = "schedule_history.json"
        self.check_results_file = "schedule_check_results.json"  # 专门存储定时检查结果
        self.keyword_check_results_file = "keyword_check_results.json"  # 新增：以关键词为索引的检查结果
        self.check_results = {}  # 存储完整的定时检查结果 {task_id: [check_records]}
        self.keyword_check_results = {}  # 新增：以关键词为索引的检查结果 {keyword: [check_records]}
        self._load_tasks()
        self._load_history()
        self._load_check_results()  # 加载定时检查结果
        self._load_keyword_check_results()  # 新增：加载关键词检查结果
        self.start()  # 自动启动调度器

    def _load_tasks(self):
        """从文件加载任务配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.tasks = [ScheduleTask.from_dict(task_data) for task_data in data]
                    # 更新所有任务的下次执行时间
                    for task in self.tasks:
                        if task.enabled:
                            task.next_run = task.calculate_next_run()
        except Exception as e:
            print(f"加载任务配置时出错: {e}")
            self.tasks = []

    def save_tasks(self):
        """保存任务配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump([task.to_dict() for task in self.tasks], f,
                         ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存任务配置时出错: {e}")

    def _load_history(self):
        """加载查询历史记录"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    self.query_history = json.load(f)
            else:
                self.query_history = {}  # 格式: {keyword: [{timestamp, data, changes}]}
        except Exception as e:
            print(f"加载历史记录时出错: {e}")
            self.query_history = {}

    def save_history(self):
        """保存查询历史记录"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.query_history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存历史记录时出错: {e}")

    def _load_check_results(self):
        """加载定时检查结果"""
        try:
            if os.path.exists(self.check_results_file):
                with open(self.check_results_file, 'r', encoding='utf-8') as f:
                    self.check_results = json.load(f)
            else:
                self.check_results = {}  # 格式: {task_id: [check_records]}
        except Exception as e:
            print(f"加载定时检查结果时出错: {e}")
            self.check_results = {}

    def save_check_results(self):
        """保存定时检查结果"""
        try:
            with open(self.check_results_file, 'w', encoding='utf-8') as f:
                json.dump(self.check_results, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存定时检查结果时出错: {e}")

    def _load_keyword_check_results(self):
        """加载以关键词为索引的检查结果"""
        try:
            if os.path.exists(self.keyword_check_results_file):
                with open(self.keyword_check_results_file, 'r', encoding='utf-8') as f:
                    self.keyword_check_results = json.load(f)
            else:
                self.keyword_check_results = {}  # 格式: {keyword: [check_records]}
        except Exception as e:
            print(f"加载关键词检查结果时出错: {e}")
            self.keyword_check_results = {}

    def save_keyword_check_results(self):
        """保存以关键词为索引的检查结果"""
        try:
            with open(self.keyword_check_results_file, 'w', encoding='utf-8') as f:
                json.dump(self.keyword_check_results, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存关键词检查结果时出错: {e}")

    def atomic_save_all_check_results(self):
        """原子性保存所有检查结果（改进版）"""
        import tempfile
        import shutil

        try:
            # 创建临时文件
            temp_files = {}

            # 任务检查结果临时文件
            with tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8', suffix='.tmp') as f:
                json.dump(self.check_results, f, ensure_ascii=False, indent=2)
                temp_files['check_results'] = f.name

            # 关键词检查结果临时文件
            with tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8', suffix='.tmp') as f:
                json.dump(self.keyword_check_results, f, ensure_ascii=False, indent=2)
                temp_files['keyword_check_results'] = f.name

            # 历史记录临时文件
            with tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8', suffix='.tmp') as f:
                json.dump(self.query_history, f, ensure_ascii=False, indent=2)
                temp_files['query_history'] = f.name

            # 验证临时文件
            for temp_file in temp_files.values():
                if not os.path.exists(temp_file) or os.path.getsize(temp_file) == 0:
                    raise Exception(f"临时文件验证失败: {temp_file}")

            # 原子性替换
            shutil.move(temp_files['check_results'], self.check_results_file)
            shutil.move(temp_files['keyword_check_results'], self.keyword_check_results_file)
            shutil.move(temp_files['query_history'], self.history_file)

            return True

        except Exception as e:
            # 清理临时文件
            for temp_file in temp_files.values():
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
            print(f"原子性保存失败: {e}")
            return False

    def _generate_task_id(self, task: ScheduleTask) -> str:
        """为任务生成唯一ID"""
        # 使用关键词和频率生成唯一标识
        keywords_str = ",".join(sorted(task.keywords))
        return f"{keywords_str}_{task.frequency}_{task.schedule_time}"

    def add_task(self, task: ScheduleTask) -> bool:
        """添加新任务"""
        if task.enabled:
            task.next_run = task.calculate_next_run()
        self.tasks.append(task)
        self.save_tasks()
        return True

    def update_task(self, index: int, task: ScheduleTask) -> bool:
        """更新任务"""
        if 0 <= index < len(self.tasks):
            if task.enabled:
                task.next_run = task.calculate_next_run()
            self.tasks[index] = task
            self.save_tasks()
            return True
        return False

    def remove_task(self, index: int) -> bool:
        """删除任务"""
        if 0 <= index < len(self.tasks):
            self.tasks.pop(index)
            self.save_tasks()
            return True
        return False

    def get_task_status(self, task: ScheduleTask) -> Dict:
        """获取任务状态"""
        with self._status_lock:
            return self.task_status.get(id(task), {
                'status': '等待中',
                'last_run': task.last_run,
                'next_run': task.next_run
            })

    def _update_task_status(self, task: ScheduleTask, status: str, details: str = None):
        """更新任务状态"""
        with self._status_lock:
            self.task_status[id(task)] = {
                'status': status,
                'last_run': task.last_run,
                'next_run': task.next_run,
                'details': details or ''
            }

    def _compare_results(self, keyword: str, new_data: dict) -> List[str]:
        """比较新旧查询结果，返回变更列表 - 基于关键词索引"""
        changes = []

        # 使用关键词检查结果进行比较
        if keyword in self.keyword_check_results and self.keyword_check_results[keyword]:
            last_record = self.keyword_check_results[keyword][-1]
            last_data = last_record.get('api_response', {}).get('data', {})

            # 这里需要根据实际的数据结构进行比较
            # 示例比较逻辑：
            if 'result' in new_data and 'result' in last_data:
                new_result = new_data['result']
                old_result = last_data['result']

                for key in new_result:
                    if key not in old_result:
                        changes.append(f"新增字段: {key}")
                    elif new_result[key] != old_result[key]:
                        changes.append(f"字段变更 {key}: {old_result[key]} -> {new_result[key]}")

                for key in old_result:
                    if key not in new_result:
                        changes.append(f"删除字段: {key}")

        return changes

    def _is_first_check_for_keyword(self, keyword: str) -> bool:
        """检查指定关键词是否为首次检查"""
        return keyword not in self.keyword_check_results or len(self.keyword_check_results[keyword]) == 0

    def _get_last_check_time_for_keyword(self, keyword: str) -> str:
        """获取指定关键词的上次检查时间"""
        if keyword in self.keyword_check_results and len(self.keyword_check_results[keyword]) > 1:
            # 获取倒数第二条记录（因为最后一条是当前正在执行的检查）
            last_record = self.keyword_check_results[keyword][-2]
            execution_time = last_record.get('execution_time', '')
            try:
                from datetime import datetime
                dt = datetime.strptime(execution_time, "%Y-%m-%d %H:%M:%S")
                return dt.strftime("%Y-%m-%d %H:%M")
            except:
                return execution_time
        return ""

    def start(self):
        """启动调度器"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self._run_scheduler)
            self.thread.daemon = True
            self.thread.start()

    def stop(self):
        """停止调度器"""
        self.running = False
        if self.thread:
            self.thread.join()
            self.thread = None

    def _run_scheduler(self):
        """运行调度器主循环"""
        while self.running:
            current_time = datetime.now()
            for task in self.tasks:
                if not task.enabled or not task.next_run:
                    continue

                next_run_time = datetime.strptime(task.next_run, "%Y-%m-%d %H:%M")
                if current_time >= next_run_time:
                    try:
                        self._update_task_status(task, '执行中', '正在查询企业信息...')
                        task_id = self._generate_task_id(task)
                        execution_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
                        results = {}
                        changes_found = False

                        # 初始化任务检查结果记录
                        if task_id not in self.check_results:
                            self.check_results[task_id] = []

                        check_record = {
                            'execution_time': execution_time,
                            'task_info': {
                                'keywords': task.keywords,
                                'frequency': task.frequency,
                                'schedule_time': task.schedule_time
                            },
                            'results': {},
                            'status': 'success',
                            'error_message': None,
                            'changes_summary': []
                        }

                        # 预先检查哪些关键词是首次检查
                        first_check_keywords = set()
                        for keyword in task.keywords:
                            if self._is_first_check_for_keyword(keyword):
                                first_check_keywords.add(keyword)

                        for keyword in task.keywords:
                            if not self.api_client:
                                raise Exception("API客户端未初始化")

                            # 执行查询 - 必须直接调用API获取最新数据
                            result = self.api_client.query_company_info(keyword)
                            if not result['success']:
                                raise Exception(f"查询失败: {result['message']}")

                            # 保存完整的API响应到检查结果
                            check_record['results'][keyword] = {
                                'api_response': result,  # 完整的API响应
                                'query_time': execution_time,
                                'success': True,
                                'is_first_check': keyword in first_check_keywords  # 标记是否为首次检查
                            }

                            # 比较结果
                            changes = self._compare_results(keyword, result['data'])
                            check_record['results'][keyword]['changes'] = changes

                            # 保存到关键词索引的检查结果
                            if keyword not in self.keyword_check_results:
                                self.keyword_check_results[keyword] = []

                            keyword_record = {
                                'execution_time': execution_time,
                                'api_response': result,  # 完整的API响应
                                'task_info': {
                                    'task_id': task_id,
                                    'frequency': task.frequency,
                                    'schedule_time': task.schedule_time
                                },
                                'changes': changes
                            }
                            self.keyword_check_results[keyword].append(keyword_record)

                            # 限制每个关键词保存的历史记录数量（保留最近100次）
                            if len(self.keyword_check_results[keyword]) > 100:
                                self.keyword_check_results[keyword] = self.keyword_check_results[keyword][-100:]

                            # 记录到历史记录（保持向后兼容）
                            if keyword not in self.query_history:
                                self.query_history[keyword] = []
                            self.query_history[keyword].append({
                                'timestamp': execution_time,
                                'data': result['data'],
                                'changes': changes
                            })

                            results[keyword] = {
                                'success': True,
                                'changes': changes,
                                'is_first_check': keyword in first_check_keywords
                            }
                            if changes:
                                changes_found = True
                                check_record['changes_summary'].extend([f"{keyword}: {change}" for change in changes])

                        # 更新任务状态
                        task.last_run = current_time.strftime("%Y-%m-%d %H:%M")
                        task.next_run = task.calculate_next_run()
                        if task.frequency == 'once':
                            task.enabled = False

                        # 生成状态详情
                        details = []
                        first_check_count = len(first_check_keywords)  # 使用预先检查的结果

                        for kw, res in results.items():
                            if res['changes']:
                                details.append(f"{kw}: 发现{len(res['changes'])}处变更")
                            else:
                                # 使用预先检查的结果
                                if res['is_first_check']:
                                    details.append(f"{kw}: 首次检查完成，已建立基准数据")
                                else:
                                    # 显示与上次检查的比较信息
                                    last_check_time = self._get_last_check_time_for_keyword(kw)
                                    if last_check_time:
                                        details.append(f"{kw}: 与{last_check_time}查询的结果相比，未发生变化")
                                    else:
                                        details.append(f"{kw}: 无历史数据可比较")

                        # 根据检查类型确定状态
                        if changes_found:
                            status = '已完成(有变更)'
                        elif first_check_count == len(task.keywords):
                            status = '已完成(首次检查)'
                        elif first_check_count > 0:
                            status = '已完成(部分首次检查)'
                        else:
                            status = '已完成(无变更)'

                        check_record['status'] = 'success'

                        # 保存检查结果记录
                        self.check_results[task_id].append(check_record)

                        # 限制每个任务保存的历史记录数量（保留最近100次）
                        if len(self.check_results[task_id]) > 100:
                            self.check_results[task_id] = self.check_results[task_id][-100:]

                        self._update_task_status(task, status, '\n'.join(details))

                        # 保存历史记录和检查结果 - 使用原子性保存
                        if not self.atomic_save_all_check_results():
                            # 如果原子性保存失败，回退到单独保存
                            print("⚠️ 原子性保存失败，使用传统保存方式")
                            self.save_history()
                            self.save_check_results()
                            self.save_keyword_check_results()

                    except Exception as e:
                        error_msg = str(e)
                        print(f"执行任务时出错: {error_msg}")

                        # 记录错误到检查结果
                        if 'check_record' in locals():
                            check_record['status'] = 'error'
                            check_record['error_message'] = error_msg
                            task_id = self._generate_task_id(task)
                            if task_id not in self.check_results:
                                self.check_results[task_id] = []
                            self.check_results[task_id].append(check_record)

                            # 同时更新关键词索引中的错误记录
                            for keyword in task.keywords:
                                if keyword not in self.keyword_check_results:
                                    self.keyword_check_results[keyword] = []

                                keyword_error_record = {
                                    'execution_time': check_record['execution_time'],
                                    'api_response': {'success': False, 'message': error_msg},
                                    'task_info': {
                                        'task_id': task_id,
                                        'frequency': task.frequency,
                                        'schedule_time': task.schedule_time
                                    },
                                    'changes': [],
                                    'error': True
                                }
                                self.keyword_check_results[keyword].append(keyword_error_record)

                            # 使用原子性保存
                            if not self.atomic_save_all_check_results():
                                print("⚠️ 错误记录原子性保存失败，使用传统保存方式")
                                self.save_check_results()
                                self.save_keyword_check_results()

                        self._update_task_status(task, '执行出错', error_msg)

            self.save_tasks()  # 保存任务状态
            time.sleep(60)  # 每分钟检查一次

    def _get_last_check_time(self, task_id: str, keyword: str) -> str:
        """获取指定任务和关键词的上次检查时间"""
        if task_id in self.check_results and len(self.check_results[task_id]) > 1:
            # 获取倒数第二次检查记录（因为最后一次是当前检查）
            last_record = self.check_results[task_id][-2]
            if keyword in last_record['results']:
                # 格式化时间显示为中文格式
                execution_time = last_record['execution_time']
                try:
                    from datetime import datetime
                    dt = datetime.strptime(execution_time, "%Y-%m-%d %H:%M:%S")
                    return dt.strftime("%Y-%m-%d %H:%M")
                except:
                    return execution_time
        return ""

    def get_task_history(self, task: ScheduleTask) -> List[Dict]:
        """获取任务的历史记录"""
        history = []
        for keyword in task.keywords:
            if keyword in self.query_history:
                history.extend(self.query_history[keyword])
        return sorted(history, key=lambda x: x['timestamp'], reverse=True)

    def get_task_check_results(self, task: ScheduleTask) -> List[Dict]:
        """获取任务的完整检查结果"""
        task_id = self._generate_task_id(task)
        if task_id in self.check_results:
            return sorted(self.check_results[task_id], key=lambda x: x['execution_time'], reverse=True)
        return []

    def get_check_result_details(self, task: ScheduleTask, execution_time: str) -> Dict:
        """获取特定执行时间的检查结果详情"""
        task_id = self._generate_task_id(task)
        if task_id in self.check_results:
            for record in self.check_results[task_id]:
                if record['execution_time'] == execution_time:
                    return record
        return {}

    def clear_all_check_history(self) -> bool:
        """清除所有定时检查历史记录"""
        try:
            # 备份当前数据（以防清除失败需要恢复）
            backup_check_results = self.check_results.copy()
            backup_keyword_check_results = self.keyword_check_results.copy()
            backup_query_history = self.query_history.copy()

            # 清除内存中的检查结果
            self.check_results = {}
            self.keyword_check_results = {}
            self.query_history = {}

            # 使用原子性保存清除结果
            if self.atomic_save_all_check_results():
                print("✅ 定时检查历史已清除")
                return True
            else:
                # 如果原子性保存失败，恢复数据并尝试传统保存
                print("⚠️ 原子性清除失败，恢复数据并尝试传统方式")
                self.check_results = backup_check_results
                self.keyword_check_results = backup_keyword_check_results
                self.query_history = backup_query_history

                # 重新清除并使用传统保存
                self.check_results = {}
                self.keyword_check_results = {}
                self.query_history = {}

                self.save_check_results()
                self.save_keyword_check_results()
                self.save_history()

                print("✅ 使用传统方式清除完成")
                return True

        except Exception as e:
            print(f"清除定时检查历史时出错: {e}")
            return False

    def get_all_check_results(self) -> Dict:
        """获取所有定时检查结果（用于查看完整历史）"""
        return self.check_results.copy()

    def get_check_result_json(self, task_id: str, execution_time: str) -> Dict:
        """获取特定检查的完整JSON响应（隐藏功能）"""
        if task_id in self.check_results:
            for record in self.check_results[task_id]:
                if record['execution_time'] == execution_time:
                    return record.get('results', {})
        return {}
