"""失信被执行人数据处理模块"""
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import pandas as pd
from openpyxl.utils import get_column_letter
import json
from openpyxl.styles import Alignment, Font, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows
import logging
import re


class DishonestPersonData:
    """失信被执行人数据结构"""
    def __init__(self):
        # 基本信息
        self.businessentity = ''         # 法人、负责人姓名
        self.amountInvolved = 0.0        # 涉案金额，单位：元
        self.areaname = ''               # 省份地区
        self.courtname = ''              # 法院
        self.unperformPart = ''          # 未履行部分
        self.staff = []                  # 法定负责人/主要负责人信息
        self.type = ''                   # 失信人类型，0代表人，1代表公司
        self.performedPart = ''          # 已履行部分
        self.iname = ''                  # 失信人名称
        self.disrupttypename = ''        # 失信被执行人行为具体情形
        self.casecode = ''               # 案号
        self.cardnum = ''                # 身份证号码/组织机构代码
        self.performance = ''            # 履行情况
        self.regdate = 0                 # 立案时间（时间戳）
        self.publishdate = 0             # 发布时间（时间戳）
        self.gistunit = ''               # 做出执行的依据单位
        self.duty = ''                   # 生效法律文书确定的义务
        self.gistid = ''                 # 执行依据文号


class DishonestDataProcessor(DishonestPersonData):
    """失信被执行人数据处理器"""

    def __init__(self, display_config: Dict = None):
        super().__init__()
        self._progress_callback = None
        self._error_callback = None
        self._total_steps = 0
        self._current_step = 0
        self._excel_title_fill = PatternFill(start_color='FFE5CC', end_color='FFE5CC', fill_type='solid')
        self._excel_title_font = Font(bold=True)
        self._display_config = display_config or {}

        # 失信人字段映射
        self._field_map = {
            'businessentity': 'businessentity',
            'amountInvolved': 'amountInvolved',
            'areaname': 'areaname',
            'courtname': 'courtname',
            'unperformPart': 'unperformPart',
            'staff': 'staff',
            'type': 'type',
            'performedPart': 'performedPart',
            'iname': 'iname',
            'disrupttypename': 'disrupttypename',
            'casecode': 'casecode',
            'cardnum': 'cardnum',
            'performance': 'performance',
            'regdate': 'regdate',
            'publishdate': 'publishdate',
            'gistunit': 'gistunit',
            'duty': 'duty',
            'gistid': 'gistid'
        }

    def update_config(self, display_config: Dict):
        """更新显示配置"""
        self._display_config = display_config

    def _process_value(self, value: Any) -> str:
        """处理字段值，清理HTML标签和格式化"""
        if value is None:
            return ''

        if isinstance(value, (int, float)):
            if isinstance(value, float) and value == int(value):
                return str(int(value))
            return str(value)

        if isinstance(value, list):
            return ', '.join(str(item) for item in value)

        if not isinstance(value, str):
            return str(value)

        # 清理HTML标签
        return self._clean_html(str(value))

    def _clean_html(self, text: str) -> str:
        """清理HTML标签和特殊字符"""
        if not isinstance(text, str):
            return str(text)

        result = text.strip()

        # 移除HTML标签
        result = re.sub(r'<[^>]+>', '', result)

        # 处理多个连续的换行符
        result = re.sub(r'\n\s*\n', '\n', result)

        # 移除行首行尾空格，但保留换行
        lines = result.split('\n')
        cleaned_lines = [line.strip() for line in lines if line.strip()]

        if len(cleaned_lines) > 1:
            result = '\n'.join(cleaned_lines)
        else:
            result = ' '.join(cleaned_lines)

        return result or ''

    def _format_timestamp(self, timestamp: int) -> str:
        """格式化时间戳为可读日期"""
        if not timestamp or timestamp == 0:
            return ''

        try:
            # 时间戳通常是毫秒级别
            if timestamp > 10000000000:  # 毫秒级时间戳
                timestamp = timestamp / 1000

            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime('%Y-%m-%d')
        except (ValueError, OSError):
            return str(timestamp)

    def parse_dishonest_info(self, content: dict) -> bool:
        """解析失信被执行人信息"""
        try:
            if not content or not isinstance(content, dict):
                self._report_error("数据为空或格式无效")
                return False

            # 获取结果数据
            result_data = content.get('result', {})
            if not result_data:
                self._report_error("未找到result数据")
                return False

            # 获取失信人列表
            items = result_data.get('items', [])

            # 检查是否为"经查无结果"的成功响应
            if not items:
                # 检查是否是300000错误码（经查无结果）
                error_code = content.get('error_code')
                if error_code == 300000:
                    # 这是有效的"无失信记录"响应，不是错误
                    logging.info("查询成功：该对象无失信记录")
                    # 清空所有字段，表示无失信记录
                    for class_field in self._field_map.values():
                        setattr(self, class_field, '')
                    return True
                else:
                    # 其他情况下的空数据才是错误
                    self._report_error("未找到失信人数据")
                    return False

            # 处理第一个失信人记录（单一查询）
            if len(items) > 0:
                item = items[0]

                # 使用字段映射更新属性
                for api_field, class_field in self._field_map.items():
                    value = item.get(api_field, '')

                    # 特殊处理时间戳字段
                    if api_field in ['regdate', 'publishdate']:
                        value = self._format_timestamp(value)
                    elif api_field == 'amountInvolved':
                        # 确保金额字段是数字类型
                        try:
                            value = float(value) if value else 0.0
                        except (ValueError, TypeError):
                            value = 0.0
                    else:
                        value = self._process_value(value)

                    setattr(self, class_field, value)

            return True

        except Exception as e:
            error_msg = f"解析失信被执行人信息时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            self._report_error(error_msg)
            return False

    def _report_error(self, message: str):
        """报告错误"""
        if self._error_callback:
            self._error_callback(message)
        else:
            logging.error(message)

    def format_for_display(self, data: dict) -> str:
        """格式化数据用于显示"""
        try:
            if not data or 'result' not in data:
                return "数据为空或格式无效"

            result_data = data['result']
            items = result_data.get('items', [])
            total = result_data.get('total', 0)

            # 检查是否为"经查无结果"的成功响应
            if not items:
                error_code = data.get('error_code')
                if error_code == 300000:
                    return "【失信被执行人查询结果】\n\n✅ 查询成功！该对象无失信被执行人记录。"
                else:
                    return "未找到失信被执行人信息"

            result = [f"【失信被执行人查询结果】"]
            result.append(f"总计找到 {total} 条记录\n")

            for i, item in enumerate(items, 1):
                result.append(f"=== 记录 {i} ===")

                # 基本信息
                iname = item.get('iname', '')
                if iname:
                    result.append(f"失信人名称: {iname}")

                person_type = item.get('type', '')
                type_desc = "公司" if person_type == "1" else "个人" if person_type == "0" else "未知"
                result.append(f"失信人类型: {type_desc}")

                cardnum = item.get('cardnum', '')
                if cardnum:
                    result.append(f"证件号码: {cardnum}")

                businessentity = item.get('businessentity', '')
                if businessentity:
                    result.append(f"法人/负责人: {businessentity}")

                # 案件信息
                casecode = item.get('casecode', '')
                if casecode:
                    result.append(f"案号: {casecode}")

                courtname = item.get('courtname', '')
                if courtname:
                    result.append(f"执行法院: {courtname}")

                areaname = item.get('areaname', '')
                if areaname:
                    result.append(f"所在地区: {areaname}")

                # 金额和履行情况
                amount = item.get('amountInvolved', 0)
                if amount:
                    result.append(f"涉案金额: {amount:,.2f} 元")

                performance = item.get('performance', '')
                if performance:
                    result.append(f"履行情况: {performance}")

                performed_part = item.get('performedPart', '')
                if performed_part and performed_part != '暂无':
                    result.append(f"已履行部分: {performed_part}")

                unperform_part = item.get('unperformPart', '')
                if unperform_part and unperform_part != '暂无':
                    result.append(f"未履行部分: {unperform_part}")

                # 失信行为
                disrupt_type = item.get('disrupttypename', '')
                if disrupt_type:
                    result.append(f"失信行为: {disrupt_type}")

                # 时间信息
                regdate = item.get('regdate', 0)
                if regdate:
                    formatted_regdate = self._format_timestamp(regdate)
                    if formatted_regdate:
                        result.append(f"立案时间: {formatted_regdate}")

                publishdate = item.get('publishdate', 0)
                if publishdate:
                    formatted_publishdate = self._format_timestamp(publishdate)
                    if formatted_publishdate:
                        result.append(f"发布时间: {formatted_publishdate}")

                # 执行依据
                gistunit = item.get('gistunit', '')
                if gistunit:
                    result.append(f"执行依据单位: {gistunit}")

                gistid = item.get('gistid', '')
                if gistid:
                    result.append(f"执行依据文号: {gistid}")

                # 义务内容
                duty = item.get('duty', '')
                if duty:
                    cleaned_duty = self._clean_html(duty)
                    if len(cleaned_duty) > 100:
                        result.append(f"法律文书确定的义务:")
                        result.append(f"    {cleaned_duty}")
                    else:
                        result.append(f"法律文书确定的义务: {cleaned_duty}")

                # 主要负责人信息
                staff = item.get('staff', [])
                if staff:
                    result.append("主要负责人信息:")
                    for staff_member in staff:
                        name = staff_member.get('name', '')
                        role = staff_member.get('role', '')
                        code = staff_member.get('code', '')
                        if name:
                            staff_info = f"  姓名: {name}"
                            if role:
                                staff_info += f", 角色: {role}"
                            if code:
                                staff_info += f", 证件号: {code}"
                            result.append(staff_info)

                result.append("-" * 50)

            return '\n'.join(result)

        except Exception as e:
            error_msg = f"格式化显示数据时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            return error_msg

    def export_to_excel(self, data_or_batch_results, filename: str, is_batch: bool = False) -> bool:
        """导出失信被执行人数据到Excel文件

        Args:
            data_or_batch_results: 单一查询数据(dict)或批量查询结果(dict of dict)
            filename: 输出文件名
            is_batch: 是否为批量查询结果
        """
        try:
            if is_batch:
                # 批量查询：每个关键词的结果作为单独的工作表
                return self._export_batch_to_excel(data_or_batch_results, filename)
            else:
                # 单一查询：使用原有逻辑
                return self._export_single_to_excel(data_or_batch_results, filename)

        except Exception as e:
            error_msg = f"导出Excel时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            self._report_error(error_msg)
            return False

    def _export_single_to_excel(self, data: dict, filename: str) -> bool:
        """导出单一查询结果到Excel"""
        if not data or 'result' not in data:
            self._report_error("数据为空或格式无效")
            return False

        result_data = data['result']
        items = result_data.get('items', [])

        if not items:
            self._report_error("没有可导出的失信人数据")
            return False

        # 准备Excel数据
        excel_data = self._prepare_excel_data(items)

        # 创建DataFrame
        df = pd.DataFrame(excel_data)

        # 导出到Excel
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='失信被执行人信息', index=False)

            # 格式化工作表
            worksheet = writer.sheets['失信被执行人信息']
            self._format_dishonest_worksheet(worksheet)

        logging.info(f"失信被执行人数据已导出到: {filename}")
        return True

    def _export_batch_to_excel(self, batch_results: dict, filename: str) -> bool:
        """导出批量查询结果到Excel，每个关键词一个工作表"""
        if not batch_results:
            self._report_error("没有可导出的批量查询数据")
            return False

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            sheet_count = 0

            for keyword, data in batch_results.items():
                if not data or 'result' not in data:
                    continue

                result_data = data['result']
                items = result_data.get('items', [])

                if not items:
                    # 即使没有数据，也创建一个空的工作表
                    empty_df = pd.DataFrame([{'失信人名称': '未找到失信记录'}])
                    sheet_name = self._get_safe_sheet_name(keyword, sheet_count)
                    empty_df.to_excel(writer, sheet_name=sheet_name, index=False)
                    sheet_count += 1
                    continue

                # 准备Excel数据
                excel_data = self._prepare_excel_data(items)

                # 创建DataFrame
                df = pd.DataFrame(excel_data)

                # 生成安全的工作表名称
                sheet_name = self._get_safe_sheet_name(keyword, sheet_count)

                # 导出到工作表
                df.to_excel(writer, sheet_name=sheet_name, index=False)

                # 格式化工作表
                worksheet = writer.sheets[sheet_name]
                self._format_dishonest_worksheet(worksheet)

                sheet_count += 1

        logging.info(f"批量失信被执行人数据已导出到: {filename}，共 {sheet_count} 个工作表")
        return True

    def _prepare_excel_data(self, items: list) -> list:
        """准备Excel数据"""
        excel_data = []

        for item in items:
            row_data = {
                '失信人名称': item.get('iname', ''),
                '失信人类型': "公司" if item.get('type') == "1" else "个人" if item.get('type') == "0" else "未知",
                '证件号码': item.get('cardnum', ''),
                '法人/负责人': item.get('businessentity', ''),
                '案号': item.get('casecode', ''),
                '执行法院': item.get('courtname', ''),
                '所在地区': item.get('areaname', ''),
                '涉案金额(元)': item.get('amountInvolved', 0),
                '履行情况': item.get('performance', ''),
                '已履行部分': item.get('performedPart', ''),
                '未履行部分': item.get('unperformPart', ''),
                '失信行为': item.get('disrupttypename', ''),
                '立案时间': self._format_timestamp(item.get('regdate', 0)),
                '发布时间': self._format_timestamp(item.get('publishdate', 0)),
                '执行依据单位': item.get('gistunit', ''),
                '执行依据文号': item.get('gistid', ''),
                '法律文书确定的义务': self._clean_html(item.get('duty', ''))
            }

            # 处理主要负责人信息
            staff = item.get('staff', [])
            if staff:
                staff_info = []
                for staff_member in staff:
                    name = staff_member.get('name', '')
                    role = staff_member.get('role', '')
                    code = staff_member.get('code', '')
                    if name:
                        info = f"{name}"
                        if role:
                            info += f"({role})"
                        if code:
                            info += f"[{code}]"
                        staff_info.append(info)
                row_data['主要负责人信息'] = '; '.join(staff_info)
            else:
                row_data['主要负责人信息'] = ''

            excel_data.append(row_data)

        return excel_data

    def _get_safe_sheet_name(self, keyword: str, sheet_index: int) -> str:
        """生成安全的Excel工作表名称"""
        # Excel工作表名称限制：最大31个字符，不能包含特殊字符
        safe_name = keyword.replace('/', '_').replace('\\', '_').replace('?', '_').replace('*', '_').replace('[', '_').replace(']', '_').replace(':', '_')

        # 限制长度
        if len(safe_name) > 25:  # 留出空间给序号
            safe_name = safe_name[:25]

        # 如果名称为空或只有特殊字符，使用默认名称
        if not safe_name.strip() or safe_name.strip() == '_':
            safe_name = f"查询结果{sheet_index + 1}"

        return safe_name

    def _format_dishonest_worksheet(self, worksheet):
        """格式化失信人Excel工作表"""
        try:
            # 自动调整列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)

                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                # 设置列宽，最小10，最大50
                adjusted_width = min(max(max_length + 2, 10), 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

            # 设置表头样式
            header_font = Font(bold=True, size=11)
            header_fill = PatternFill(start_color='FFE5CC', end_color='FFE5CC', fill_type='solid')
            center_aligned = Alignment(horizontal='center', vertical='center')

            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_aligned

            # 冻结首行
            worksheet.freeze_panes = 'A2'

        except Exception as e:
            logging.error(f"格式化失信人工作表时出错: {str(e)}")

    def get_summary_info(self, data: dict) -> Dict[str, Any]:
        """获取失信人数据摘要信息"""
        try:
            if not data or 'result' not in data:
                return {}

            result_data = data['result']
            items = result_data.get('items', [])
            total = result_data.get('total', 0)

            if not items:
                return {'total': 0, 'items_count': 0}

            # 统计信息
            company_count = sum(1 for item in items if item.get('type') == '1')
            person_count = sum(1 for item in items if item.get('type') == '0')

            # 涉案金额统计
            amounts = [item.get('amountInvolved', 0) for item in items if item.get('amountInvolved')]
            total_amount = sum(amounts) if amounts else 0
            avg_amount = total_amount / len(amounts) if amounts else 0

            # 履行情况统计
            performance_stats = {}
            for item in items:
                performance = item.get('performance', '未知')
                performance_stats[performance] = performance_stats.get(performance, 0) + 1

            return {
                'total': total,
                'items_count': len(items),
                'company_count': company_count,
                'person_count': person_count,
                'total_amount': total_amount,
                'avg_amount': avg_amount,
                'performance_stats': performance_stats
            }

        except Exception as e:
            logging.error(f"获取摘要信息时出错: {str(e)}")
            return {}
