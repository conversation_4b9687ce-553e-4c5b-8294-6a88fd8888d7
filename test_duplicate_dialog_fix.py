#!/usr/bin/env python3
"""
Test script to verify the duplicate dialog fix for DishonestQueryWorker
"""

import sys
import logging
from unittest.mock import Mock, MagicMock
from PyQt5.QtCore import QTimer, QThread, pyqtSignal
from PyQt5.QtWidgets import QApplication

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')

# Mock the API client
class MockAPIClient:
    def query_dishonest_person(self, keyword):
        logging.info(f"Mock API call for: {keyword}")
        return {
            'success': True,
            'data': {
                'keyword': keyword,
                'error_code': 200000,
                'results': [{'name': keyword, 'status': 'clean'}]
            }
        }

# Import the worker class
sys.path.append('.')
from main_window import DishonestQueryWorker

class TestDuplicateDialogFix:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.api_client = MockAPIClient()
        self.signal_counts = {}
        self.test_results = []
        
    def reset_counters(self):
        """Reset signal counters for new test"""
        self.signal_counts = {
            'finished': 0,
            'batch_completed': 0,
            'status_updated': 0,
            'query_completed': 0
        }
        self.test_results = []
        
    def test_no_cache_scenario(self):
        """Test scenario with no cached data (fresh API calls)"""
        logging.info("=== Testing No Cache Scenario ===")
        self.reset_counters()
        
        keywords = ["测试公司1", "测试公司2"]
        query_results = {}  # No cache
        worker = DishonestQueryWorker(self.api_client, keywords, query_results)
        
        # Connect signals and count them
        worker.finished.connect(self.on_finished)
        worker.batch_completed.connect(self.on_batch_completed)
        worker.status_updated.connect(self.on_status_updated)
        worker.query_completed.connect(self.on_query_completed)
        worker.query_error.connect(self.on_query_error)
        
        # Start worker
        worker.start()
        
        # Process events for a reasonable time
        QTimer.singleShot(5000, self.app.quit)
        self.app.exec_()
        
        return self.signal_counts.copy()
        
    def test_with_cache_scenario(self):
        """Test scenario with cached data"""
        logging.info("=== Testing With Cache Scenario ===")
        self.reset_counters()
        
        keywords = ["测试公司1", "测试公司2"]
        query_results = {
            "测试公司1": {"cached": True, "data": "cached_data"}
        }  # One cached result
        worker = DishonestQueryWorker(self.api_client, keywords, query_results)
        
        # Mock the cache prompt to automatically choose "use cache"
        def mock_cache_decision(keyword, use_cache):
            logging.info(f"Mock cache decision for {keyword}: use_cache={use_cache}")
            worker.handle_cache_decision(keyword, True)  # Always use cache
        
        # Connect signals and count them
        worker.finished.connect(self.on_finished)
        worker.batch_completed.connect(self.on_batch_completed)
        worker.status_updated.connect(self.on_status_updated)
        worker.query_completed.connect(self.on_query_completed)
        worker.query_error.connect(self.on_query_error)
        worker.cache_prompt_needed.connect(lambda keyword, cache_data: mock_cache_decision(keyword, True))
        
        # Start worker
        worker.start()
        
        # Process events for a reasonable time
        QTimer.singleShot(5000, self.app.quit)
        self.app.exec_()
        
        return self.signal_counts.copy()
        
    def on_finished(self):
        """QThread built-in finished signal handler"""
        self.signal_counts['finished'] += 1
        logging.info(f"QThread finished signal received (count: {self.signal_counts['finished']})")
        
    def on_batch_completed(self):
        """Custom batch_completed signal handler"""
        self.signal_counts['batch_completed'] += 1
        logging.info(f"Custom batch_completed signal received (count: {self.signal_counts['batch_completed']})")
        self.app.quit()  # End test when batch is completed
        
    def on_status_updated(self, message):
        self.signal_counts['status_updated'] += 1
        logging.info(f"Status: {message}")
        
    def on_query_completed(self, result):
        self.signal_counts['query_completed'] += 1
        keyword = result['keyword']
        logging.info(f"Query completed for: {keyword}")
        
    def on_query_error(self, error):
        logging.error(f"Query error: {error}")

def main():
    """Run the tests"""
    tester = TestDuplicateDialogFix()
    
    # Test 1: No cache scenario
    print("\n" + "="*60)
    print("TEST 1: No Cache Scenario (Fresh API Calls)")
    print("="*60)
    results1 = tester.test_no_cache_scenario()
    print("Signal Counts:", results1)
    
    # Test 2: With cache scenario
    print("\n" + "="*60)
    print("TEST 2: With Cache Scenario")
    print("="*60)
    tester.app = QApplication(sys.argv)  # Create new app instance
    results2 = tester.test_with_cache_scenario()
    print("Signal Counts:", results2)
    
    print("\n" + "="*60)
    print("TEST RESULTS ANALYSIS")
    print("="*60)
    
    # Analyze results
    print(f"Test 1 (No Cache):")
    print(f"  - QThread finished signals: {results1['finished']}")
    print(f"  - Custom batch_completed signals: {results1['batch_completed']}")
    print(f"  - Query completed signals: {results1['query_completed']}")
    print(f"  - Status updated signals: {results1['status_updated']}")
    
    print(f"\nTest 2 (With Cache):")
    print(f"  - QThread finished signals: {results2['finished']}")
    print(f"  - Custom batch_completed signals: {results2['batch_completed']}")
    print(f"  - Query completed signals: {results2['query_completed']}")
    print(f"  - Status updated signals: {results2['status_updated']}")
    
    # Determine if fix is successful
    test1_success = results1['batch_completed'] == 1 and results1['finished'] <= 1
    test2_success = results2['batch_completed'] == 1 and results2['finished'] <= 1
    
    print(f"\n" + "="*60)
    print("FINAL RESULTS")
    print("="*60)
    print(f"Test 1 (No Cache): {'PASS' if test1_success else 'FAIL'}")
    print(f"Test 2 (With Cache): {'PASS' if test2_success else 'FAIL'}")
    print(f"Overall Fix Status: {'SUCCESS' if test1_success and test2_success else 'NEEDS WORK'}")
    
    if test1_success and test2_success:
        print("\n✅ Duplicate dialog fix is working correctly!")
        print("   - Only one batch_completed signal per test")
        print("   - QThread finished signal is controlled")
        print("   - No duplicate completion dialogs should appear")
    else:
        print("\n❌ Duplicate dialog fix needs more work")
        print("   - Check signal emission logic")
        print("   - Verify signal connections")

if __name__ == '__main__':
    main()
