#!/usr/bin/env python3
"""
Test script to verify the DishonestQueryWorker batch query fix
"""

import sys
import logging
from unittest.mock import Mock, MagicMock
from PyQt5.QtCore import QTimer, QThread, pyqtSignal
from PyQt5.QtWidgets import QApplication

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')

# Mock the API client
class MockAPIClient:
    def query_dishonest_person(self, keyword):
        logging.info(f"Mock API call for: {keyword}")
        return {
            'success': True,
            'data': {
                'keyword': keyword,
                'error_code': 200000,
                'results': [{'name': keyword, 'status': 'clean'}]
            }
        }

# Import the worker class (we'll need to modify the import path)
sys.path.append('.')
from main_window import DishonestQueryWorker

class TestBatchQuery:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.api_client = MockAPIClient()
        self.query_results = {}  # Empty cache
        self.test_results = []
        
    def test_empty_keywords(self):
        """Test with empty keywords list"""
        logging.info("=== Testing empty keywords ===")
        keywords = []
        worker = DishonestQueryWorker(self.api_client, keywords, self.query_results)
        
        # Connect signals
        worker.status_updated.connect(self.on_status_updated)
        worker.finished.connect(self.on_finished)
        worker.query_error.connect(self.on_query_error)
        
        # Start worker
        worker.start()
        
        # Process events for a short time
        QTimer.singleShot(1000, self.app.quit)
        self.app.exec_()
        
        return self.test_results
        
    def test_single_keyword(self):
        """Test with single keyword"""
        logging.info("=== Testing single keyword ===")
        keywords = ["测试公司1"]
        worker = DishonestQueryWorker(self.api_client, keywords, self.query_results)
        
        # Connect signals
        worker.status_updated.connect(self.on_status_updated)
        worker.finished.connect(self.on_finished)
        worker.query_error.connect(self.on_query_error)
        worker.query_completed.connect(self.on_query_completed)
        
        # Start worker
        worker.start()
        
        # Process events for a short time
        QTimer.singleShot(3000, self.app.quit)
        self.app.exec_()
        
        return self.test_results
        
    def test_multiple_keywords(self):
        """Test with multiple keywords"""
        logging.info("=== Testing multiple keywords ===")
        keywords = ["测试公司1", "测试公司2", "测试公司3"]
        worker = DishonestQueryWorker(self.api_client, keywords, self.query_results)
        
        # Connect signals
        worker.status_updated.connect(self.on_status_updated)
        worker.finished.connect(self.on_finished)
        worker.query_error.connect(self.on_query_error)
        worker.query_completed.connect(self.on_query_completed)
        worker.progress_updated.connect(self.on_progress_updated)
        
        # Start worker
        worker.start()
        
        # Process events for a longer time
        QTimer.singleShot(10000, self.app.quit)
        self.app.exec_()
        
        return self.test_results
        
    def on_status_updated(self, message):
        logging.info(f"Status: {message}")
        self.test_results.append(f"STATUS: {message}")
        
    def on_finished(self):
        logging.info("Worker finished")
        self.test_results.append("FINISHED")
        self.app.quit()
        
    def on_query_error(self, error):
        logging.error(f"Query error: {error}")
        self.test_results.append(f"ERROR: {error}")
        
    def on_query_completed(self, result):
        keyword = result['keyword']
        logging.info(f"Query completed for: {keyword}")
        self.test_results.append(f"COMPLETED: {keyword}")
        
    def on_progress_updated(self, current, total):
        logging.info(f"Progress: {current}/{total}")
        self.test_results.append(f"PROGRESS: {current}/{total}")

def main():
    """Run the tests"""
    tester = TestBatchQuery()
    
    # Test 1: Empty keywords
    print("\n" + "="*50)
    print("TEST 1: Empty Keywords")
    print("="*50)
    tester.test_results = []
    results1 = tester.test_empty_keywords()
    print("Results:", results1)
    
    # Test 2: Single keyword
    print("\n" + "="*50)
    print("TEST 2: Single Keyword")
    print("="*50)
    tester.test_results = []
    tester.app = QApplication(sys.argv)  # Create new app instance
    results2 = tester.test_single_keyword()
    print("Results:", results2)
    
    # Test 3: Multiple keywords
    print("\n" + "="*50)
    print("TEST 3: Multiple Keywords")
    print("="*50)
    tester.test_results = []
    tester.app = QApplication(sys.argv)  # Create new app instance
    results3 = tester.test_multiple_keywords()
    print("Results:", results3)
    
    print("\n" + "="*50)
    print("TEST SUMMARY")
    print("="*50)
    print(f"Test 1 (Empty): {'PASS' if 'FINISHED' in results1 else 'FAIL'}")
    print(f"Test 2 (Single): {'PASS' if 'COMPLETED: 测试公司1' in results2 and 'FINISHED' in results2 else 'FAIL'}")
    print(f"Test 3 (Multiple): {'PASS' if len([r for r in results3 if 'COMPLETED:' in r]) == 3 and 'FINISHED' in results3 else 'FAIL'}")

if __name__ == '__main__':
    main()
